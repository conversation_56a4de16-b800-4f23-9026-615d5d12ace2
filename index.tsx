'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function Page() {
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTrack, setCurrentTrack] = useState(null);
    const [currentLanguage, setCurrentLanguage] = useState('zh');

    const languages = {
        zh: { name: '中文', flag: '🇨🇳' },
        en: { name: 'English', flag: '🇺🇸' },
        ja: { name: '日本語', flag: '🇯🇵' },
        ko: { name: '한국어', flag: '🇰🇷' },
    };

    const translations = {
        zh: {
            siteName: '静音助眠',
            navigation: {
                sounds: '白噪音',
                blog: '睡眠博客',
                about: '关于我们',
            },
            buttons: {
                freeTrial: '免费试用',
                experience: '🎧 立即体验',
                learnMore: '📚 了解更多',
                play: '▶️ 播放',
                pause: '⏸️ 暂停',
                viewMore: '查看更多文章',
                startFree: '免费开始使用',
            },
            hero: {
                title: '让白噪音陪伴你的美梦',
                titleHighlight: '白噪音',
                description:
                    '精心挑选的自然白噪音，帮助你快速入睡，提升睡眠质量。科学证明，白噪音能够屏蔽环境干扰，让大脑更容易进入深度睡眠状态。',
            },
            sections: {
                soundsTitle: '精选白噪音分类',
                blogTitle: '睡眠知识博客',
                ctaTitle: '开始你的优质睡眠之旅',
                ctaDescription:
                    '加入数万用户，体验白噪音带来的深度睡眠。今晚就让我们陪伴你进入甜美梦乡。',
            },
            categories: [
                { name: '雨声', description: '温柔的雨滴声' },
                { name: '海浪', description: '舒缓的海浪声' },
                { name: '森林', description: '自然森林环境音' },
                { name: '咖啡厅', description: '温馨咖啡厅氛围' },
                { name: '篝火', description: '温暖的篝火声' },
                { name: '风声', description: '轻柔的微风声' },
            ],

            features: [
                {
                    title: '助眠专用',
                    description: '专为改善睡眠质量设计的白噪音，让你快速进入深度睡眠',
                },
                { title: '专注提升', description: '屏蔽外界干扰，提高工作和学习时的专注力' },
                { title: '放松身心', description: '缓解压力和焦虑，帮助身心达到完全放松的状态' },
            ],

            typeLabels: {
                noise: '白噪音',
                things: '物品',
                transport: '交通',
                places: '场所',
                urban: '城市',
                animals: '动物',
                rain: '雨声',
                nature: '自然',
            },
            footer: {
                tagline: '让白噪音陪伴你的每一个美梦',
                product: '产品',
                resources: '资源',
                contact: '联系我们',
                copyright: '© 2024 静音助眠. 保留所有权利.',
                supportedLanguages: '支持的语言：',
            },
        },
        en: {
            siteName: 'Sleep Sounds',
            navigation: {
                sounds: 'White Noise',
                blog: 'Sleep Blog',
                about: 'About Us',
            },
            buttons: {
                freeTrial: 'Free Trial',
                experience: '🎧 Try Now',
                learnMore: '📚 Learn More',
                play: '▶️ Play',
                pause: '⏸️ Pause',
                viewMore: 'View More Articles',
                startFree: 'Start Free',
            },
            hero: {
                title: 'Let White Noise Accompany Your Sweet Dreams',
                titleHighlight: 'White Noise',
                description:
                    'Carefully selected natural white noise to help you fall asleep quickly and improve sleep quality. Scientifically proven, white noise can block environmental interference and make it easier for the brain to enter deep sleep.',
            },
            sections: {
                soundsTitle: 'Featured White Noise Categories',
                blogTitle: 'Sleep Knowledge Blog',
                ctaTitle: 'Start Your Quality Sleep Journey',
                ctaDescription:
                    'Join tens of thousands of users and experience deep sleep brought by white noise. Let us accompany you into sweet dreams tonight.',
            },
            categories: [
                { name: 'Rain', description: 'Gentle raindrops sound' },
                { name: 'Ocean Waves', description: 'Soothing ocean waves' },
                { name: 'Forest', description: 'Natural forest ambience' },
                { name: 'Coffee Shop', description: 'Cozy coffee shop atmosphere' },
                { name: 'Campfire', description: 'Warm campfire sounds' },
                { name: 'Wind', description: 'Gentle breeze sounds' },
            ],

            features: [
                {
                    title: 'Sleep Aid',
                    description:
                        'White noise designed specifically to improve sleep quality, helping you quickly enter deep sleep',
                },
                {
                    title: 'Focus Enhancement',
                    description:
                        'Block external distractions and improve concentration during work and study',
                },
                {
                    title: 'Relaxation',
                    description:
                        'Relieve stress and anxiety, helping body and mind achieve complete relaxation',
                },
            ],

            typeLabels: {
                noise: 'White Noise',
                things: 'Things',
                transport: 'Transport',
                places: 'Places',
                urban: 'Urban',
                animals: 'Animals',
                rain: 'Rain',
                nature: 'Nature',
            },
            footer: {
                tagline: 'Let white noise accompany every sweet dream',
                product: 'Product',
                resources: 'Resources',
                contact: 'Contact',
                copyright: '© 2024 Sleep Sounds. All rights reserved.',
                supportedLanguages: 'Supported Languages:',
            },
        },
        ja: {
            siteName: 'スリープサウンド',
            navigation: {
                sounds: 'ホワイトノイズ',
                blog: '睡眠ブログ',
                about: '私たちについて',
            },
            buttons: {
                freeTrial: '無料体験',
                experience: '🎧 今すぐ体験',
                learnMore: '📚 詳しく見る',
                play: '▶️ 再生',
                pause: '⏸️ 一時停止',
                viewMore: 'もっと見る',
                startFree: '無料で始める',
            },
            hero: {
                title: 'ホワイトノイズがあなたの美しい夢に寄り添います',
                titleHighlight: 'ホワイトノイズ',
                description:
                    '厳選された自然のホワイトノイズで、素早い入眠と睡眠の質の向上をサポート。科学的に証明されたホワイトノイズが環境の妨害をブロックし、脳が深い眠りに入りやすくします。',
            },
            sections: {
                soundsTitle: '厳選ホワイトノイズカテゴリー',
                blogTitle: '睡眠知識ブログ',
                ctaTitle: '質の高い睡眠の旅を始めましょう',
                ctaDescription:
                    '数万人のユーザーと一緒に、ホワイトノイズがもたらす深い眠りを体験してください。今夜、私たちがあなたを甘い夢の世界へご案内します。',
            },
            categories: [
                { name: '雨音', description: '優しい雨粒の音' },
                { name: '波音', description: '心地よい波の音' },
                { name: '森', description: '自然の森の環境音' },
                { name: 'カフェ', description: '温かいカフェの雰囲気' },
                { name: '焚き火', description: '温かい焚き火の音' },
                { name: '風音', description: '優しいそよ風の音' },
            ],

            features: [
                {
                    title: '睡眠専用',
                    description:
                        '睡眠の質向上のために特別に設計されたホワイトノイズで、深い眠りへと導きます',
                },
                {
                    title: '集中力向上',
                    description: '外部の妨害をブロックし、仕事や勉強の集中力を高めます',
                },
                {
                    title: 'リラクゼーション',
                    description: 'ストレスや不安を和らげ、心身の完全なリラックス状態を実現します',
                },
            ],

            typeLabels: {
                noise: 'ホワイトノイズ',
                things: '物音',
                transport: '交通',
                places: '場所',
                urban: '都市',
                animals: '動物',
                rain: '雨音',
                nature: '自然',
            },
            footer: {
                tagline: 'ホワイトノイズがすべての美しい夢に寄り添います',
                product: '製品',
                resources: 'リソース',
                contact: 'お問い合わせ',
                copyright: '© 2024 スリープサウンド. 全著作権所有.',
                supportedLanguages: 'サポート言語：',
            },
        },
        ko: {
            siteName: '슬립 사운드',
            navigation: {
                sounds: '화이트 노이즈',
                blog: '수면 블로그',
                about: '회사 소개',
            },
            buttons: {
                freeTrial: '무료 체험',
                experience: '🎧 지금 체험',
                learnMore: '📚 자세히 보기',
                play: '▶️ 재생',
                pause: '⏸️ 일시정지',
                viewMore: '더 많은 글 보기',
                startFree: '무료로 시작하기',
            },
            hero: {
                title: '화이트 노이즈가 당신의 아름다운 꿈과 함께합니다',
                titleHighlight: '화이트 노이즈',
                description:
                    '엄선된 자연 화이트 노이즈로 빠른 잠들기와 수면의 질 향상을 도와드립니다. 과학적으로 입증된 화이트 노이즈가 환경 방해를 차단하고 뇌가 깊은 잠에 들기 쉽게 만듭니다.',
            },
            sections: {
                soundsTitle: '엄선된 화이트 노이즈 카테고리',
                blogTitle: '수면 지식 블로그',
                ctaTitle: '양질의 수면 여행을 시작하세요',
                ctaDescription:
                    '수만 명의 사용자와 함께 화이트 노이즈가 가져다주는 깊은 잠을 경험해보세요. 오늘 밤 우리가 당신을 달콤한 꿈나라로 안내해드립니다.',
            },
            categories: [
                { name: '빗소리', description: '부드러운 빗방울 소리' },
                { name: '파도소리', description: '편안한 파도 소리' },
                { name: '숲', description: '자연 숲 환경음' },
                { name: '카페', description: '따뜻한 카페 분위기' },
                { name: '모닥불', description: '따뜻한 모닥불 소리' },
                { name: '바람소리', description: '부드러운 바람 소리' },
            ],

            features: [
                {
                    title: '수면 전용',
                    description:
                        '수면의 질 향상을 위해 특별히 설계된 화이트 노이즈로 깊은 잠에 빠르게 빠져들게 합니다',
                },
                {
                    title: '집중력 향상',
                    description: '외부 방해를 차단하고 업무와 학습 시 집중력을 높입니다',
                },
                {
                    title: '심신 이완',
                    description: '스트레스와 불안을 완화하여 심신의 완전한 이완 상태를 도와줍니다',
                },
            ],

            typeLabels: {
                noise: '화이트 노이즈',
                things: '사물',
                transport: '교통',
                places: '장소',
                urban: '도시',
                animals: '동물',
                rain: '빗소리',
                nature: '자연',
            },
            footer: {
                tagline: '화이트 노이즈가 모든 아름다운 꿈과 함께합니다',
                product: '제품',
                resources: '리소스',
                contact: '연락처',
                copyright: '© 2024 슬립 사운드. 모든 권리 보유.',
                supportedLanguages: '지원 언어:',
            },
        },
    };

    const t = translations[currentLanguage];

    const whiteNoiseCategories = [
        { icon: '🌧️', type: 'rain' },
        { icon: '🌊', type: 'nature' },
        { icon: '🌲', type: 'nature' },
        { icon: '☕', type: 'places' },
        { icon: '🔥', type: 'nature' },
        { icon: '💨', type: 'nature' },
    ];

    const getTypeColor = (type) => {
        const colors = {
            noise: 'bg-blue-100 text-blue-800',
            things: 'bg-gray-100 text-gray-800',
            transport: 'bg-yellow-100 text-yellow-800',
            places: 'bg-purple-100 text-purple-800',
            urban: 'bg-orange-100 text-orange-800',
            animals: 'bg-green-100 text-green-800',
            rain: 'bg-cyan-100 text-cyan-800',
            nature: 'bg-emerald-100 text-emerald-800',
        };
        return colors[type] || 'bg-gray-100 text-gray-800';
    };

    const blogPosts = [
        {
            title: '白噪音如何改善睡眠质量',
            excerpt: '了解白噪音的科学原理，以及它如何帮助大脑放松进入深度睡眠状态...',
            date: '2024年1月15日',
            readTime: '5分钟阅读',
        },
        {
            title: '不同类型噪音的睡眠效果对比',
            excerpt: '粉红噪音、棕色噪音与白噪音的区别，哪种更适合你的睡眠需求...',
            date: '2024年1月10日',
            readTime: '7分钟阅读',
        },
        {
            title: '创造完美的睡眠环境指南',
            excerpt: '除了声音，温度、光线、湿度等因素如何影响睡眠质量...',
            date: '2024年1月5日',
            readTime: '6分钟阅读',
        },
    ];

    const handlePlay = (trackName) => {
        if (currentTrack === trackName && isPlaying) {
            setIsPlaying(false);
            setCurrentTrack(null);
        } else {
            setIsPlaying(true);
            setCurrentTrack(trackName);
        }
    };

    return (
        <div
            className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50"
            data-oid="deo:k67"
        >
            {/* Header */}
            <header
                className="bg-white/80 backdrop-blur-sm border-b border-gray-100 sticky top-0 z-50"
                data-oid="lxrzoaa"
            >
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-oid="gbfii8y">
                    <div className="flex justify-between items-center py-4" data-oid="297.cco">
                        <div className="flex items-center space-x-2" data-oid="923sigh">
                            <div
                                className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center"
                                data-oid="rlvp7l0"
                            >
                                <span className="text-white text-sm" data-oid="nneguhl">
                                    🎵
                                </span>
                            </div>
                            <h1 className="text-xl font-semibold text-gray-800" data-oid="4qasgw6">
                                {t.siteName}
                            </h1>
                        </div>
                        <nav className="hidden md:flex space-x-8" data-oid="3xsv5yv">
                            <a
                                href="#sounds"
                                className="text-gray-600 hover:text-indigo-600 transition-colors"
                                data-oid="4uvq84n"
                            >
                                {t.navigation.sounds}
                            </a>
                            <a
                                href="#blog"
                                className="text-gray-600 hover:text-indigo-600 transition-colors"
                                data-oid="-gq7n7s"
                            >
                                {t.navigation.blog}
                            </a>
                            <a
                                href="#about"
                                className="text-gray-600 hover:text-indigo-600 transition-colors"
                                data-oid="l_ue9x9"
                            >
                                {t.navigation.about}
                            </a>
                        </nav>
                        <div className="flex items-center space-x-4" data-oid="_z1sd8c">
                            <select
                                value={currentLanguage}
                                onChange={(e) => setCurrentLanguage(e.target.value)}
                                className="bg-white border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                data-oid="bkho15u"
                            >
                                {Object.entries(languages).map(([code, lang]) => (
                                    <option key={code} value={code} data-oid="b9rl0m7">
                                        {lang.flag} {lang.name}
                                    </option>
                                ))}
                            </select>
                            <Link
                                href="/signup"
                                className="inline-block bg-indigo-600 text-white px-4 py-2 rounded-full hover:bg-indigo-700 transition-colors"
                                data-oid="06fmdca"
                            >
                                {t.buttons.freeTrial}
                            </Link>
                        </div>
                    </div>
                </div>
            </header>

            {/* Hero Section */}
            <section className="py-20 px-4 sm:px-6 lg:px-8" data-oid="49-mogg">
                <div className="max-w-4xl mx-auto text-center" data-oid="k9ls:tw">
                    <h2
                        className="text-5xl md:text-6xl font-light text-gray-800 mb-6 leading-tight"
                        data-oid="hwv1d:o"
                    >
                        {currentLanguage === 'zh' ? (
                            <>
                                让
                                <span className="text-indigo-600" data-oid="lg50lud">
                                    {t.hero.titleHighlight}
                                </span>
                                <br data-oid="qtw-6lv" />
                                陪伴你的美梦
                            </>
                        ) : (
                            <>
                                Let{' '}
                                <span className="text-indigo-600" data-oid="w1yx:h2">
                                    {t.hero.titleHighlight}
                                </span>
                                <br data-oid="fx8suxa" />
                                Accompany Your Sweet Dreams
                            </>
                        )}
                    </h2>
                    <p
                        className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto leading-relaxed"
                        data-oid="m6xa:25"
                    >
                        {t.hero.description}
                    </p>
                    <div
                        className="flex flex-col sm:flex-row gap-4 justify-center"
                        data-oid="tobvppm"
                    >
                        <Link
                            href="/player"
                            className="inline-block bg-indigo-600 text-white px-8 py-4 rounded-full text-lg hover:bg-indigo-700 transition-all transform hover:scale-105"
                            data-oid="z4b-ywz"
                        >
                            {t.buttons.experience}
                        </Link>
                        <button
                            className="border-2 border-indigo-600 text-indigo-600 px-8 py-4 rounded-full text-lg hover:bg-indigo-50 transition-colors"
                            data-oid="o918uwm"
                        >
                            {t.buttons.learnMore}
                        </button>
                    </div>
                </div>
            </section>

            {/* White Noise Categories */}
            <section
                id="sounds"
                className="py-16 px-4 sm:px-6 lg:px-8 bg-white/50"
                data-oid="nwsg67f"
            >
                <div className="max-w-6xl mx-auto" data-oid=":xrqoxm">
                    <h3
                        className="text-3xl font-light text-center text-gray-800 mb-12"
                        data-oid="91k0_1s"
                    >
                        {t.sections.soundsTitle}
                    </h3>
                    <div
                        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                        data-oid="n1cx5nk"
                    >
                        {whiteNoiseCategories.map((category, index) => (
                            <div
                                key={index}
                                className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all border border-gray-100"
                                data-oid=":z2:s2q"
                            >
                                <div
                                    className="flex justify-between items-start mb-4"
                                    data-oid="1z_y6-s"
                                >
                                    <div className="text-4xl" data-oid="ugqeqa-">
                                        {category.icon}
                                    </div>
                                    <span
                                        className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(category.type)}`}
                                        data-oid="ig0:6.c"
                                    >
                                        {t.typeLabels[category.type]}
                                    </span>
                                </div>
                                <h4
                                    className="text-xl font-medium text-gray-800 mb-2"
                                    data-oid="_wjlv7t"
                                >
                                    {t.categories[index].name}
                                </h4>
                                <p className="text-gray-600 mb-4" data-oid="lttudy7">
                                    {t.categories[index].description}
                                </p>
                                <button
                                    onClick={() => handlePlay(t.categories[index].name)}
                                    className={`w-full py-3 rounded-full transition-all ${
                                        currentTrack === t.categories[index].name && isPlaying
                                            ? 'bg-indigo-600 text-white'
                                            : 'bg-gray-100 text-gray-700 hover:bg-indigo-100'
                                    }`}
                                    data-oid="4y2f7if"
                                >
                                    {currentTrack === t.categories[index].name && isPlaying
                                        ? t.buttons.pause
                                        : t.buttons.play}
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Features */}
            <section className="py-16 px-4 sm:px-6 lg:px-8" data-oid="2uc:joo">
                <div className="max-w-6xl mx-auto" data-oid="i1:sa:s">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8" data-oid="e-cj9l_">
                        <div className="text-center" data-oid="i488r..">
                            <div
                                className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4"
                                data-oid="rchn19s"
                            >
                                <span className="text-2xl" data-oid="mhzi2-m">
                                    🌙
                                </span>
                            </div>
                            <h4
                                className="text-xl font-medium text-gray-800 mb-2"
                                data-oid="-ih3383"
                            >
                                {t.features[0].title}
                            </h4>
                            <p className="text-gray-600" data-oid="0lmytz8">
                                {t.features[0].description}
                            </p>
                        </div>
                        <div className="text-center" data-oid="dhsb5sh">
                            <div
                                className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4"
                                data-oid="rh3dz6d"
                            >
                                <span className="text-2xl" data-oid="tqjyc9u">
                                    🎯
                                </span>
                            </div>
                            <h4
                                className="text-xl font-medium text-gray-800 mb-2"
                                data-oid="o74xtjd"
                            >
                                {t.features[1].title}
                            </h4>
                            <p className="text-gray-600" data-oid="wsm-.dr">
                                {t.features[1].description}
                            </p>
                        </div>
                        <div className="text-center" data-oid="mwdyrfh">
                            <div
                                className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
                                data-oid="03kjc6t"
                            >
                                <span className="text-2xl" data-oid="nr2lmp7">
                                    🧘
                                </span>
                            </div>
                            <h4
                                className="text-xl font-medium text-gray-800 mb-2"
                                data-oid="hr79o2r"
                            >
                                {t.features[2].title}
                            </h4>
                            <p className="text-gray-600" data-oid="3_c0-7m">
                                {t.features[2].description}
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Blog Section */}
            <section id="blog" className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50" data-oid="xyx83n6">
                <div className="max-w-6xl mx-auto" data-oid="hj-jswn">
                    <h3
                        className="text-3xl font-light text-center text-gray-800 mb-12"
                        data-oid="x.mk.dx"
                    >
                        {t.sections.blogTitle}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8" data-oid="o.av4dj">
                        {blogPosts.map((post, index) => (
                            <article
                                key={index}
                                className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all"
                                data-oid="lb46o-h"
                            >
                                <h4
                                    className="text-xl font-medium text-gray-800 mb-3 hover:text-indigo-600 cursor-pointer"
                                    data-oid="tuugr-4"
                                >
                                    {post.title}
                                </h4>
                                <p
                                    className="text-gray-600 mb-4 leading-relaxed"
                                    data-oid="g3w6i29"
                                >
                                    {post.excerpt}
                                </p>
                                <div
                                    className="flex justify-between items-center text-sm text-gray-500"
                                    data-oid="fsot1i0"
                                >
                                    <span data-oid="8vlublr">{post.date}</span>
                                    <span data-oid="z56.bl2">{post.readTime}</span>
                                </div>
                            </article>
                        ))}
                    </div>
                    <div className="text-center mt-12" data-oid=":5pf7mv">
                        <button
                            className="border-2 border-indigo-600 text-indigo-600 px-8 py-3 rounded-full hover:bg-indigo-50 transition-colors"
                            data-oid="titz2pa"
                        >
                            {t.buttons.viewMore}
                        </button>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section
                className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-indigo-600 to-purple-600"
                data-oid="zp75bj5"
            >
                <div className="max-w-4xl mx-auto text-center" data-oid="h1-gzhn">
                    <h3 className="text-4xl font-light text-white mb-6" data-oid="c_z-53q">
                        {t.sections.ctaTitle}
                    </h3>
                    <p
                        className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto"
                        data-oid="x._tf8m"
                    >
                        {t.sections.ctaDescription}
                    </p>
                    <Link
                        href="/signup"
                        className="inline-block bg-white text-indigo-600 px-8 py-4 rounded-full text-lg font-medium hover:bg-gray-50 transition-all transform hover:scale-105"
                        data-oid="a2hi56z"
                    >
                        {t.buttons.startFree}
                    </Link>
                </div>
            </section>

            {/* Footer */}
            <footer
                className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8"
                data-oid="t2gcek:"
            >
                <div className="max-w-6xl mx-auto" data-oid="s:y-7un">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8" data-oid="6_hb2sz">
                        <div data-oid="fb75.p8">
                            <div className="flex items-center space-x-2 mb-4" data-oid="ht2vcqn">
                                <div
                                    className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center"
                                    data-oid="n7t81-x"
                                >
                                    <span className="text-white text-sm" data-oid="d2d1tx4">
                                        🎵
                                    </span>
                                </div>
                                <span className="text-xl font-semibold" data-oid="610g-ni">
                                    {t.siteName}
                                </span>
                            </div>
                            <p className="text-gray-400" data-oid="ituwo08">
                                {t.footer.tagline}
                            </p>
                        </div>
                        <div data-oid="y-relfk">
                            <h5 className="font-medium mb-4" data-oid="-xt9-pi">
                                {t.footer.product}
                            </h5>
                            <ul className="space-y-2 text-gray-400" data-oid="catti4g">
                                <li data-oid="3y1zkm9">
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                        data-oid="dkyk._v"
                                    >
                                        {currentLanguage === 'zh'
                                            ? '白噪音库'
                                            : currentLanguage === 'en'
                                              ? 'White Noise Library'
                                              : currentLanguage === 'ja'
                                                ? 'ホワイトノイズライブラリ'
                                                : '화이트 노이즈 라이브러리'}
                                    </a>
                                </li>
                                <li data-oid="tn7_d0y">
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                        data-oid="dfjk6xa"
                                    >
                                        {currentLanguage === 'zh'
                                            ? '定时功能'
                                            : currentLanguage === 'en'
                                              ? 'Timer Function'
                                              : currentLanguage === 'ja'
                                                ? 'タイマー機能'
                                                : '타이머 기능'}
                                    </a>
                                </li>
                                <li data-oid="xbthbj0">
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                        data-oid="p7zixte"
                                    >
                                        {currentLanguage === 'zh'
                                            ? '混音器'
                                            : currentLanguage === 'en'
                                              ? 'Mixer'
                                              : currentLanguage === 'ja'
                                                ? 'ミキサー'
                                                : '믹서'}
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div data-oid="k2433cq">
                            <h5 className="font-medium mb-4" data-oid="a1kv91-">
                                {t.footer.resources}
                            </h5>
                            <ul className="space-y-2 text-gray-400" data-oid="ma7rlql">
                                <li data-oid=":6i8lyo">
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                        data-oid="be58u0s"
                                    >
                                        {t.navigation.blog}
                                    </a>
                                </li>
                                <li data-oid="7:_90:8">
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                        data-oid="y48::j."
                                    >
                                        {currentLanguage === 'zh'
                                            ? '使用指南'
                                            : currentLanguage === 'en'
                                              ? 'User Guide'
                                              : currentLanguage === 'ja'
                                                ? '使用ガイド'
                                                : '사용 가이드'}
                                    </a>
                                </li>
                                <li data-oid="n_q7h9u">
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                        data-oid="zmp.ld:"
                                    >
                                        {currentLanguage === 'zh'
                                            ? '科学研究'
                                            : currentLanguage === 'en'
                                              ? 'Scientific Research'
                                              : currentLanguage === 'ja'
                                                ? '科学的研究'
                                                : '과학적 연구'}
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div data-oid="u_-u:x:">
                            <h5 className="font-medium mb-4" data-oid="o6hpe5z">
                                {t.footer.contact}
                            </h5>
                            <ul className="space-y-2 text-gray-400" data-oid="h8ljlvr">
                                <li data-oid="favhgwu">
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                        data-oid="jay06ma"
                                    >
                                        {currentLanguage === 'zh'
                                            ? '客服支持'
                                            : currentLanguage === 'en'
                                              ? 'Customer Support'
                                              : currentLanguage === 'ja'
                                                ? 'カスタマーサポート'
                                                : '고객 지원'}
                                    </a>
                                </li>
                                <li data-oid="amr3yxc">
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                        data-oid="e6q8cxy"
                                    >
                                        {currentLanguage === 'zh'
                                            ? '意见反馈'
                                            : currentLanguage === 'en'
                                              ? 'Feedback'
                                              : currentLanguage === 'ja'
                                                ? 'フィードバック'
                                                : '피드백'}
                                    </a>
                                </li>
                                <li data-oid="31w0yi2">
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                        data-oid="ci8b4pt"
                                    >
                                        {currentLanguage === 'zh'
                                            ? '合作洽谈'
                                            : currentLanguage === 'en'
                                              ? 'Partnership'
                                              : currentLanguage === 'ja'
                                                ? 'パートナーシップ'
                                                : '파트너십'}
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div className="border-t border-gray-800 mt-8 pt-8" data-oid="rhpoa5h">
                        <div
                            className="flex flex-col md:flex-row justify-between items-center text-gray-400"
                            data-oid="jt.l15l"
                        >
                            <p data-oid="0fv3qgp">{t.footer.copyright}</p>
                            <div className="mt-4 md:mt-0" data-oid="_ho._02">
                                <span className="text-sm mr-4" data-oid="kcvpmk1">
                                    {t.footer.supportedLanguages}
                                </span>
                                {Object.entries(languages).map(([code, lang]) => (
                                    <button
                                        key={code}
                                        onClick={() => setCurrentLanguage(code)}
                                        className={`text-sm mx-1 px-2 py-1 rounded transition-colors ${
                                            currentLanguage === code
                                                ? 'bg-indigo-600 text-white'
                                                : 'hover:text-white'
                                        }`}
                                        data-oid="hehybu9"
                                    >
                                        {lang.flag} {lang.name}
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    );
}
