import { notFound } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import { AudioGrid } from '@/components/AudioCard';
import { audioData } from '@/data/audioData';

interface CategoryPageProps {
  params: {
    locale: string;
    category: string;
  };
}

export async function generateStaticParams() {
  // 获取所有分类
  const categories = Array.from(new Set(audioData.map(audio => audio.category)));
  
  return categories.map((category) => ({
    category: category.toLowerCase(),
  }));
}

export async function generateMetadata({ params }: CategoryPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'metadata' });
  const categoryName = decodeURIComponent(params.category);
  
  return {
    title: t('category.title', { category: categoryName }),
    description: t('category.description', { category: categoryName }),
  };
}

export default function CategoryPage({ params }: CategoryPageProps) {
  const t = useTranslations('sounds');
  const categoryName = decodeURIComponent(params.category);
  
  // 获取该分类的音频
  const categoryAudios = audioData.filter(
    audio => audio.category.toLowerCase() === categoryName.toLowerCase()
  );

  // 如果分类不存在，返回404
  if (categoryAudios.length === 0) {
    notFound();
  }

  // 获取分类信息
  const categoryInfo = getCategoryInfo(categoryName);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-6">
          <a href="/" className="hover:text-amber-600 dark:hover:text-amber-400">
            {t('home')}
          </a>
          <span>/</span>
          <a href="/sounds" className="hover:text-amber-600 dark:hover:text-amber-400">
            {t('sounds')}
          </a>
          <span>/</span>
          <span className="text-gray-900 dark:text-gray-100 font-medium">
            {categoryInfo.icon} {categoryName}
          </span>
        </nav>

        {/* 分类头部 */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20 rounded-2xl flex items-center justify-center">
              <span className="text-3xl">{categoryInfo.icon}</span>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                {categoryName} {t('sounds')}
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-400">
                {categoryAudios.length} {t('audioFiles')}
              </p>
            </div>
          </div>
          
          <p className="text-gray-600 dark:text-gray-400 max-w-3xl">
            {categoryInfo.description}
          </p>
        </div>

        {/* 分类统计 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">
              {categoryAudios.length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {t('totalSounds')}
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">
              {Math.round(categoryAudios.reduce((sum, audio) => sum + (audio.scientificRating || 0), 0) / categoryAudios.length * 10) / 10}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {t('avgRating')}
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">
              {Array.from(new Set(categoryAudios.flatMap(audio => audio.tags || []))).length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {t('uniqueTags')}
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">
              {Math.round(categoryAudios.reduce((sum, audio) => sum + (audio.duration || 0), 0) / 60)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {t('totalMinutes')}
            </div>
          </div>
        </div>

        {/* 音频网格 */}
        <AudioGrid
          audios={categoryAudios}
          variant="detailed"
          showSearch={true}
          showFilter={false} // 已经在分类页面，不需要分类过滤
          showSort={true}
          emptyMessage={t('noCategoryAudios', { category: categoryName })}
        />
      </div>
    </div>
  );
}

// 获取分类信息
function getCategoryInfo(category: string) {
  const categoryInfoMap: Record<string, { icon: string; description: string }> = {
    rain: {
      icon: '🌧️',
      description: 'Soothing rain sounds to help you relax and focus. From gentle drizzles to heavy downpours.',
    },
    nature: {
      icon: '🌿',
      description: 'Natural ambient sounds from forests, rivers, and outdoor environments.',
    },
    noise: {
      icon: '🔊',
      description: 'White, pink, and brown noise for concentration and sleep.',
    },
    animals: {
      icon: '🐾',
      description: 'Calming animal sounds including birds, cats, and other peaceful creatures.',
    },
    things: {
      icon: '🏠',
      description: 'Household and everyday sounds that create a cozy atmosphere.',
    },
    transport: {
      icon: '🚗',
      description: 'Transportation sounds like trains, cars, and planes for travel ambiance.',
    },
    urban: {
      icon: '🏙️',
      description: 'City sounds and urban environments for background ambiance.',
    },
    places: {
      icon: '📍',
      description: 'Specific location sounds from cafes, libraries, and other places.',
    },
  };

  return categoryInfoMap[category.toLowerCase()] || {
    icon: '🎵',
    description: 'Audio sounds for relaxation and focus.',
  };
}
