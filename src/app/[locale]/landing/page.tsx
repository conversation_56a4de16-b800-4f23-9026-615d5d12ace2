import { useTranslations, useLocale } from 'next-intl';
import { setRequestLocale } from 'next-intl/server';
import Link from 'next/link';

// 组件导入
import LanguageSelector from '@/components/LanguageSelector';
import HeroSection from '@/components/HeroSection';
import CategoryCard from '@/components/CategoryCard';
import FeatureCard from '@/components/FeatureCard';
import BlogCard from '@/components/BlogCard';
import CTASection from '@/components/CTASection';

type Props = {
  params: { locale: string };
};

export default function LandingPage({ params: { locale } }: Props) {
  // 启用静态渲染优化
  setRequestLocale(locale);

  const t = useTranslations('landing');
  const currentLocale = useLocale();

  // 白噪音分类数据
  const whiteNoiseCategories = [
    { icon: '🌧️', type: 'rain' },
    { icon: '🌊', type: 'nature' },
    { icon: '🌲', type: 'nature' },
    { icon: '☕', type: 'places' },
    { icon: '🔥', type: 'nature' },
    { icon: '💨', type: 'nature' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">🎵</span>
              </div>
              <h1 className="text-xl font-semibold text-gray-800">
                {t('siteName')}
              </h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a
                href="#sounds"
                className="text-gray-600 hover:text-indigo-600 transition-colors"
              >
                {t('navigation.sounds')}
              </a>
              <a
                href="#blog"
                className="text-gray-600 hover:text-indigo-600 transition-colors"
              >
                {t('navigation.blog')}
              </a>
              <Link
                href={`/${locale}/about`}
                className="text-gray-600 hover:text-indigo-600 transition-colors"
              >
                {t('navigation.about')}
              </Link>
            </nav>
            <div className="flex items-center space-x-4">
              <LanguageSelector />
              <Link
                href={`/${locale}/sounds`}
                className="inline-block bg-indigo-600 text-white px-4 py-2 rounded-full hover:bg-indigo-700 transition-colors"
              >
                {t('buttons.freeTrial')}
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <HeroSection />

      {/* White Noise Categories */}
      <section
        id="sounds"
        className="py-16 px-4 sm:px-6 lg:px-8 bg-white/50"
      >
        <div className="max-w-6xl mx-auto">
          <h3 className="text-3xl font-light text-center text-gray-800 mb-12">
            {t('sections.soundsTitle')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {whiteNoiseCategories.map((category, index) => (
              <CategoryCard
                key={index}
                icon={category.icon}
                type={category.type}
                name={t(`categories.${index}.name` as any)}
                description={t(`categories.${index}.description` as any)}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <FeatureCard
              icon="🌙"
              title={t('features.0.title')}
              description={t('features.0.description')}
              bgColor="bg-indigo-100"
            />
            <FeatureCard
              icon="🎯"
              title={t('features.1.title')}
              description={t('features.1.description')}
              bgColor="bg-purple-100"
            />
            <FeatureCard
              icon="🧘"
              title={t('features.2.title')}
              description={t('features.2.description')}
              bgColor="bg-green-100"
            />
          </div>
        </div>
      </section>

      {/* Blog Section */}
      <section id="blog" className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <h3 className="text-3xl font-light text-center text-gray-800 mb-12">
            {t('sections.blogTitle')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[0, 1, 2].map((index) => (
              <BlogCard
                key={index}
                post={{
                  title: t(`blogPosts.${index}.title` as any),
                  excerpt: t(`blogPosts.${index}.excerpt` as any),
                  date: t(`blogPosts.${index}.date` as any),
                  readTime: t(`blogPosts.${index}.readTime` as any),
                }}
              />
            ))}
          </div>
          <div className="text-center mt-12">
            <button className="border-2 border-indigo-600 text-indigo-600 px-8 py-3 rounded-full hover:bg-indigo-50 transition-colors">
              {t('buttons.viewMore')}
            </button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <CTASection />

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">🎵</span>
                </div>
                <span className="text-xl font-semibold">{t('siteName')}</span>
              </div>
              <p className="text-gray-400">{t('footer.tagline')}</p>
            </div>
            <div>
              <h5 className="font-medium mb-4">{t('footer.product')}</h5>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href={`/${locale}/sounds`} className="hover:text-white transition-colors">
                    {currentLocale === 'zh' ? '白噪音库' : 'White Noise Library'}
                  </Link>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {currentLocale === 'zh' ? '定时功能' : 'Timer Function'}
                  </a>
                </li>
                <li>
                  <Link href={`/${locale}/mix`} className="hover:text-white transition-colors">
                    {currentLocale === 'zh' ? '混音器' : 'Mixer'}
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium mb-4">{t('footer.resources')}</h5>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#blog" className="hover:text-white transition-colors">
                    {t('navigation.blog')}
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {currentLocale === 'zh' ? '使用指南' : 'User Guide'}
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {currentLocale === 'zh' ? '科学研究' : 'Scientific Research'}
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium mb-4">{t('footer.contact')}</h5>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {currentLocale === 'zh' ? '客服支持' : 'Customer Support'}
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {currentLocale === 'zh' ? '意见反馈' : 'Feedback'}
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {currentLocale === 'zh' ? '合作洽谈' : 'Partnership'}
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center text-gray-400">
              <p>{t('footer.copyright')}</p>
              <div className="mt-4 md:mt-0">
                <span className="text-sm mr-4">{t('footer.supportedLanguages')}</span>
                <LanguageSelector variant="footer" />
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
