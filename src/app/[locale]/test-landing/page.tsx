import { useTranslations } from 'next-intl';
import { setRequestLocale } from 'next-intl/server';

// 组件导入
import LanguageSelector from '@/components/LanguageSelector';
import HeroSection from '@/components/HeroSection';
import CategoryCard from '@/components/CategoryCard';
import FeatureCard from '@/components/FeatureCard';
import BlogCard from '@/components/BlogCard';
import CTASection from '@/components/CTASection';

type Props = {
  params: { locale: string };
};

export default function TestLandingPage({ params: { locale } }: Props) {
  // 启用静态渲染优化
  setRequestLocale(locale);
  
  const t = useTranslations('landing');

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">🎵</span>
              </div>
              <h1 className="text-xl font-semibold text-gray-800">
                {t('siteName')} - 测试页面
              </h1>
            </div>
            <LanguageSelector />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <HeroSection />

      {/* Test Components */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-2xl font-bold text-center mb-8">组件测试</h2>
          
          {/* Category Cards */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold mb-4">分类卡片</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <CategoryCard
                icon="🌧️"
                type="rain"
                name="雨声"
                description="舒缓的雨声帮助放松"
              />
              <CategoryCard
                icon="🌊"
                type="nature"
                name="海浪"
                description="海浪声带来宁静"
              />
              <CategoryCard
                icon="🔥"
                type="nature"
                name="篝火"
                description="温暖的篝火声"
              />
            </div>
          </div>

          {/* Feature Cards */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold mb-4">特色功能</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <FeatureCard
                icon="🌙"
                title="助眠音效"
                description="科学配比的白噪音"
                bgColor="bg-indigo-100"
              />
              <FeatureCard
                icon="🎯"
                title="专注模式"
                description="提高工作效率"
                bgColor="bg-purple-100"
              />
              <FeatureCard
                icon="🧘"
                title="冥想引导"
                description="放松身心"
                bgColor="bg-green-100"
              />
            </div>
          </div>

          {/* Blog Cards */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold mb-4">博客文章</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <BlogCard
                post={{
                  title: "白噪音的科学原理",
                  excerpt: "了解白噪音如何帮助改善睡眠质量...",
                  date: "2024-01-15",
                  readTime: "5分钟阅读"
                }}
              />
              <BlogCard
                post={{
                  title: "如何选择适合的睡眠音效",
                  excerpt: "不同类型的音效适合不同的人群...",
                  date: "2024-01-10",
                  readTime: "3分钟阅读"
                }}
              />
              <BlogCard
                post={{
                  title: "创建完美的睡眠环境",
                  excerpt: "除了音效，还有哪些因素影响睡眠...",
                  date: "2024-01-05",
                  readTime: "7分钟阅读"
                }}
              />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <CTASection />

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto text-center">
          <p className="text-gray-400">测试页面 - 所有组件正常工作</p>
          <div className="mt-4">
            <LanguageSelector variant="footer" />
          </div>
        </div>
      </footer>
    </div>
  );
}
