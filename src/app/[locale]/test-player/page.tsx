'use client';

import { useTranslations } from 'next-intl';
import { AudioCard } from '@/components/AudioCard/AudioCard';
import { MultilingualAudioItem } from '@/types/audio';

// 测试音频数据
const testAudio: MultilingualAudioItem = {
  id: 'test-rain-1',
  title: {
    zh: '轻柔雨声',
    en: 'Gentle Rain'
  },
  description: {
    zh: '舒缓的雨声，帮助放松和睡眠',
    en: 'Soothing rain sounds for relaxation and sleep'
  },
  category: 'rain',
  tags: ['rain', 'nature', 'sleep'],
  duration: 180,
  fileUrl: '/audio/rain/gentle-rain.mp3',
  imageUrl: '/images/rain/gentle-rain.jpg',
  rating: 4.8,
  playCount: 15420,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
};

export default function TestPlayerPage() {
  const t = useTranslations('common');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            音频播放器测试页面
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            点击下面的音频卡片来测试播放器功能
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AudioCard 
            audio={testAudio}
            variant="card"
            showDuration={true}
            showRating={true}
            showPlayCount={true}
          />
          <AudioCard 
            audio={{
              ...testAudio,
              id: 'test-rain-2',
              title: {
                zh: '暴雨声',
                en: 'Heavy Rain'
              },
              description: {
                zh: '强烈的暴雨声，适合深度专注',
                en: 'Intense rain sounds for deep focus'
              }
            }}
            variant="card"
            showDuration={true}
            showRating={true}
            showPlayCount={true}
          />
          <AudioCard 
            audio={{
              ...testAudio,
              id: 'test-rain-3',
              title: {
                zh: '雨滴声',
                en: 'Rain Drops'
              },
              description: {
                zh: '清脆的雨滴声，营造宁静氛围',
                en: 'Crisp rain drops creating peaceful atmosphere'
              }
            }}
            variant="list"
            showDuration={true}
            showRating={false}
            showPlayCount={false}
          />
        </div>

        <div className="mt-12 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            测试说明
          </h2>
          <ul className="space-y-2 text-gray-600 dark:text-gray-400">
            <li>• 点击任意音频卡片应该会显示底部播放器</li>
            <li>• 播放器应该显示当前播放的音频信息</li>
            <li>• 播放器应该有播放/暂停、停止、音量控制等功能</li>
            <li>• 在桌面端应该显示定时器和混音按钮</li>
            <li>• 在移动端这些按钮应该被隐藏</li>
            <li>• 播放器应该支持最小化和关闭</li>
            <li>• 播放器应该有平滑的动画效果</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
