import { useTranslations, useLocale } from 'next-intl';
import { setRequestLocale } from 'next-intl/server';

// 组件导入
import { PageLayout } from '@/components/Layout';
import HeroSection from '@/components/HeroSection';
import CategoryCard from '@/components/CategoryCard';
import FeatureCard from '@/components/FeatureCard';
import BlogCard from '@/components/BlogCard';
import CTASection from '@/components/CTASection';

type Props = {
  params: { locale: string };
};

export default function HomePage({ params: { locale } }: Props) {
  // 启用静态渲染优化
  setRequestLocale(locale);

  const t = useTranslations('landing');
  const currentLocale = useLocale();

  // 白噪音分类数据
  const whiteNoiseCategories = [
    { icon: '🌧️', type: 'rain' },
    { icon: '🌊', type: 'nature' },
    { icon: '🌲', type: 'nature' },
    { icon: '☕', type: 'places' },
    { icon: '🔥', type: 'nature' },
    { icon: '💨', type: 'nature' },
  ];

  return (
    <PageLayout headerVariant="landing">{/* 使用Landing页面的Header样式 */}

      {/* Hero Section */}
      <HeroSection />

      {/* 白噪音分类展示 */}
      <section id="sounds" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('categories.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('categories.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {whiteNoiseCategories.map((category, index) => (
              <CategoryCard
                key={index}
                icon={category.icon}
                type={category.type}
                locale={currentLocale}
              />
            ))}
          </div>
        </div>
      </section>

      {/* 特色功能 */}
      <section id="features" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('features.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('features.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon="🧠"
              title={t('features.scientific.title')}
              description={t('features.scientific.description')}
            />
            <FeatureCard
              icon="🎚️"
              title={t('features.customizable.title')}
              description={t('features.customizable.description')}
            />
            <FeatureCard
              icon="📱"
              title={t('features.responsive.title')}
              description={t('features.responsive.description')}
            />
            <FeatureCard
              icon="🌙"
              title={t('features.sleepMode.title')}
              description={t('features.sleepMode.description')}
            />
            <FeatureCard
              icon="🎯"
              title={t('features.focus.title')}
              description={t('features.focus.description')}
            />
            <FeatureCard
              icon="🌍"
              title={t('features.multilingual.title')}
              description={t('features.multilingual.description')}
            />
          </div>
        </div>
      </section>

      {/* 博客预览 */}
      <section id="blog" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('blog.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('blog.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <BlogCard
              post={{
                title: t('blog.posts.sleepScience.title'),
                excerpt: t('blog.posts.sleepScience.excerpt'),
                date: t('blog.posts.sleepScience.date'),
                readTime: t('blog.posts.sleepScience.readTime')
              }}
            />
            <BlogCard
              post={{
                title: t('blog.posts.whiteNoise.title'),
                excerpt: t('blog.posts.whiteNoise.excerpt'),
                date: t('blog.posts.whiteNoise.date'),
                readTime: t('blog.posts.whiteNoise.readTime')
              }}
            />
            <BlogCard
              post={{
                title: t('blog.posts.productivity.title'),
                excerpt: t('blog.posts.productivity.excerpt'),
                date: t('blog.posts.productivity.date'),
                readTime: t('blog.posts.productivity.readTime')
              }}
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <CTASection />
    </PageLayout>
  );
}
