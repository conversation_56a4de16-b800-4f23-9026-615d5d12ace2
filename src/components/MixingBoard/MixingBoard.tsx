'use client';

import { useState, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { clsx } from 'clsx';
import { useAudioStore } from '@/store/audioStore';
import { MultilingualAudioItem } from '@/types/audio';
import { MixingChannel } from './MixingChannel';
import { VolumeControl } from '@/components/AudioPlayer/VolumeControl';
import { audioData } from '@/data/audioData';

interface MixingBoardProps {
  className?: string;
}

export function MixingBoard({ className }: MixingBoardProps) {
  const t = useTranslations('mixing');
  const [showAudioSelector, setShowAudioSelector] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  const {
    mixingChannels,
    maxChannels,
    masterVolume,
    addMixingChannel,
    removeMixingChannel,
    updateChannelVolume,
    setMasterVolume,
  } = useAudioStore();

  // 获取可用的音频列表
  const availableAudios = audioData.filter(audio => 
    !mixingChannels.some(channel => channel.soundId === audio.id)
  );

  // 按分类过滤音频
  const filteredAudios = selectedCategory === 'all' 
    ? availableAudios 
    : availableAudios.filter(audio => audio.category === selectedCategory);

  // 获取所有分类
  const categories = Array.from(new Set(audioData.map(audio => audio.category))).sort();

  // 添加音频到混音板
  const handleAddAudio = useCallback((audio: MultilingualAudioItem) => {
    const success = addMixingChannel(audio);
    if (success) {
      setShowAudioSelector(false);
    }
  }, [addMixingChannel]);

  // 移除频道
  const handleRemoveChannel = useCallback((channelId: string) => {
    removeMixingChannel(channelId);
  }, [removeMixingChannel]);

  // 更新频道音量
  const handleChannelVolumeChange = useCallback((channelId: string, volume: number) => {
    updateChannelVolume(channelId, volume);
  }, [updateChannelVolume]);

  // 获取音频数据
  const getAudioById = (soundId: string) => {
    return audioData.find(audio => audio.id === soundId);
  };

  // 获取音频图标
  const getAudioIcon = (category: string) => {
    const iconMap: Record<string, string> = {
      rain: '🌧️',
      nature: '🌿',
      noise: '🔊',
      animals: '🐾',
      things: '🏠',
      transport: '🚗',
      urban: '🏙️',
      places: '📍',
    };
    return iconMap[category.toLowerCase()] || '🎵';
  };

  return (
    <div className={clsx('space-y-6', className)}>
      {/* 混音板头部 */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {t('title')}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t('description', { max: maxChannels })}
            </p>
          </div>
          
          {/* 添加音频按钮 */}
          <button
            onClick={() => setShowAudioSelector(true)}
            disabled={mixingChannels.length >= maxChannels}
            className={clsx(
              'inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors',
              'focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2',
              mixingChannels.length >= maxChannels
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500'
                : 'bg-amber-500 text-white hover:bg-amber-600 shadow-sm hover:shadow-md'
            )}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            {t('addAudio')}
          </button>
        </div>

        {/* 主音量控制 */}
        <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {t('masterVolume')}
            </h3>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {Math.round(masterVolume * 100)}%
            </span>
          </div>
          <VolumeControl
            volume={masterVolume}
            onVolumeChange={setMasterVolume}
            showIcon={true}
            showValue={false}
            size="md"
          />
        </div>
      </div>

      {/* 混音频道 */}
      <div className="space-y-4">
        {mixingChannels.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-xl border-2 border-dashed border-gray-200 dark:border-gray-700 p-12 text-center">
            <div className="w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600">
              <svg fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {t('noChannels')}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {t('addFirstAudio')}
            </p>
            <button
              onClick={() => setShowAudioSelector(true)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-amber-500 text-white rounded-lg font-medium hover:bg-amber-600 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              {t('addAudio')}
            </button>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2">
            {mixingChannels.map((channel) => {
              const audio = getAudioById(channel.soundId);
              if (!audio) return null;

              return (
                <MixingChannel
                  key={channel.id}
                  channel={channel}
                  audio={audio}
                  isPlaying={false} // TODO: 实现实际播放状态
                  isLoading={false} // TODO: 实现实际加载状态
                  onPlay={() => {}} // TODO: 实现播放功能
                  onPause={() => {}} // TODO: 实现暂停功能
                  onStop={() => {}} // TODO: 实现停止功能
                  onVolumeChange={(volume) => handleChannelVolumeChange(channel.id, volume)}
                  onMute={() => {}} // TODO: 实现静音功能
                  onRemove={() => handleRemoveChannel(channel.id)}
                />
              );
            })}
          </div>
        )}
      </div>

      {/* 音频选择器模态框 */}
      {showAudioSelector && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            {/* 背景遮罩 */}
            <div 
              className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
              onClick={() => setShowAudioSelector(false)}
            />

            {/* 模态框内容 */}
            <div className="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-2xl">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  {t('selectAudio')}
                </h3>
                <button
                  onClick={() => setShowAudioSelector(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* 分类过滤 */}
              <div className="mb-4">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-amber-500"
                >
                  <option value="all">{t('allCategories')}</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {getAudioIcon(category)} {category}
                    </option>
                  ))}
                </select>
              </div>

              {/* 音频列表 */}
              <div className="max-h-96 overflow-y-auto">
                {filteredAudios.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    {t('noAvailableAudios')}
                  </div>
                ) : (
                  <div className="grid gap-3 sm:grid-cols-2">
                    {filteredAudios.map((audio) => (
                      <button
                        key={audio.id}
                        onClick={() => handleAddAudio(audio)}
                        className="flex items-center gap-3 p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:border-amber-300 dark:hover:border-amber-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <div className="w-10 h-10 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20 rounded-lg flex items-center justify-center flex-shrink-0">
                          <span className="text-lg">{getAudioIcon(audio.category)}</span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                            {audio.title.en}
                          </h4>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            {audio.category}
                          </p>
                        </div>
                        {audio.scientificRating && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            ⭐ {audio.scientificRating.toFixed(1)}
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
