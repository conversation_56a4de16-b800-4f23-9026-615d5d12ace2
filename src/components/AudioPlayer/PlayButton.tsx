'use client';

import { useTranslations } from 'next-intl';
import { clsx } from 'clsx';

interface PlayButtonProps {
  isPlaying: boolean;
  isLoading: boolean;
  onPlay: () => void;
  onPause: () => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'ghost';
  disabled?: boolean;
  className?: string;
}

export function PlayButton({
  isPlaying,
  isLoading,
  onPlay,
  onPause,
  size = 'md',
  variant = 'primary',
  disabled = false,
  className,
}: PlayButtonProps) {
  const t = useTranslations('common');

  const handleClick = () => {
    if (disabled || isLoading) return;
    
    if (isPlaying) {
      onPause();
    } else {
      onPlay();
    }
  };

  const sizeClasses = {
    sm: 'w-8 h-8 p-1.5',
    md: 'w-12 h-12 p-3',
    lg: 'w-16 h-16 p-4',
  };

  const variantClasses = {
    primary: 'bg-amber-500 hover:bg-amber-600 text-white shadow-lg hover:shadow-xl',
    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200',
    ghost: 'bg-transparent hover:bg-gray-100 text-gray-600 dark:hover:bg-gray-800 dark:text-gray-400',
  };

  const iconSize = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-6 h-6',
  };

  return (
    <button
      onClick={handleClick}
      disabled={disabled || isLoading}
      className={clsx(
        // 基础样式
        'relative rounded-full transition-all duration-200 ease-in-out',
        'focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        'transform hover:scale-105 active:scale-95',
        
        // 尺寸样式
        sizeClasses[size],
        
        // 变体样式
        variantClasses[variant],
        
        // 自定义类名
        className
      )}
      aria-label={isPlaying ? t('pause') : t('play')}
      title={isPlaying ? t('pause') : t('play')}
    >
      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className={clsx(
            'animate-spin rounded-full border-2 border-current border-t-transparent',
            iconSize[size]
          )} />
        </div>
      )}

      {/* 播放/暂停图标 */}
      {!isLoading && (
        <div className="flex items-center justify-center">
          {isPlaying ? (
            // 暂停图标
            <svg
              className={iconSize[size]}
              fill="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z" />
            </svg>
          ) : (
            // 播放图标
            <svg
              className={clsx(iconSize[size], 'ml-0.5')} // 稍微向右偏移以视觉居中
              fill="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path d="M8 5v14l11-7z" />
            </svg>
          )}
        </div>
      )}

      {/* 波纹效果 */}
      {isPlaying && !isLoading && (
        <div className="absolute inset-0 rounded-full animate-ping bg-current opacity-20" />
      )}
    </button>
  );
}

// 预设的播放按钮变体
export function PrimaryPlayButton(props: Omit<PlayButtonProps, 'variant'>) {
  return <PlayButton {...props} variant="primary" />;
}

export function SecondaryPlayButton(props: Omit<PlayButtonProps, 'variant'>) {
  return <PlayButton {...props} variant="secondary" />;
}

export function GhostPlayButton(props: Omit<PlayButtonProps, 'variant'>) {
  return <PlayButton {...props} variant="ghost" />;
}

// 大型播放按钮（用于主播放器）
export function LargePlayButton(props: Omit<PlayButtonProps, 'size'>) {
  return <PlayButton {...props} size="lg" />;
}

// 小型播放按钮（用于列表项）
export function SmallPlayButton(props: Omit<PlayButtonProps, 'size'>) {
  return <PlayButton {...props} size="sm" />;
}
