import { MultilingualAudioItem } from '@/types/audio';

export const audioData: MultilingualAudioItem[] = [
  // Rain Category (8 files)
  {
    id: 'rain_gentle_drizzle',
    filename: 'gentle_drizzle.mp3',
    category: 'Rain',
    title: {
      en: 'Gentle Drizzle',
      zh: '细雨绵绵'
    },
    description: {
      en: 'Soft, gentle rain falling steadily for relaxation',
      zh: '轻柔细雨，持续降落，带来放松感受'
    },
    tags: ['gentle', 'soft', 'continuous'],
    duration: 3600, // 1 hour
    scientificRating: 9.2,
    sleepEffectiveness: 9.5,
    focusEffectiveness: 8.8
  },
  {
    id: 'rain_heavy_downpour',
    filename: 'heavy_downpour.mp3',
    category: 'Rain',
    title: {
      en: 'Heavy Downpour',
      zh: '大雨倾盆'
    },
    description: {
      en: 'Intense rainfall with thunder for deep relaxation',
      zh: '强烈降雨伴随雷声，深度放松'
    },
    tags: ['intense', 'thunder', 'powerful'],
    duration: 3600,
    scientificRating: 8.9,
    sleepEffectiveness: 9.0,
    focusEffectiveness: 7.5
  },
  {
    id: 'rain_on_roof',
    filename: 'rain_on_roof.mp3',
    category: 'Rain',
    title: {
      en: 'Rain on Roof',
      zh: '屋顶雨声'
    },
    description: {
      en: 'Rhythmic rain drops hitting the roof',
      zh: '有节奏的雨滴敲击屋顶声'
    },
    tags: ['rhythmic', 'cozy', 'indoor'],
    duration: 3600,
    scientificRating: 9.0,
    sleepEffectiveness: 9.2,
    focusEffectiveness: 8.5
  },
  {
    id: 'rain_with_wind',
    filename: 'rain_with_wind.mp3',
    category: 'Rain',
    title: {
      en: 'Rain with Wind',
      zh: '风雨交加'
    },
    description: {
      en: 'Rain accompanied by gentle wind sounds',
      zh: '雨声伴随轻柔风声'
    },
    tags: ['wind', 'natural', 'atmospheric'],
    duration: 3600,
    scientificRating: 8.7,
    sleepEffectiveness: 8.8,
    focusEffectiveness: 8.0
  },
  {
    id: 'rain_forest',
    filename: 'forest_rain.mp3',
    category: 'Rain',
    title: {
      en: 'Forest Rain',
      zh: '森林雨声'
    },
    description: {
      en: 'Rain falling in a lush forest environment',
      zh: '雨水在茂密森林中降落'
    },
    tags: ['forest', 'nature', 'peaceful'],
    duration: 3600,
    scientificRating: 9.1,
    sleepEffectiveness: 9.3,
    focusEffectiveness: 8.9
  },
  {
    id: 'rain_light_shower',
    filename: 'light_shower.mp3',
    category: 'Rain',
    title: {
      en: 'Light Shower',
      zh: '小雨阵阵'
    },
    description: {
      en: 'Light, intermittent rain shower',
      zh: '轻柔间歇性小雨'
    },
    tags: ['light', 'intermittent', 'fresh'],
    duration: 3600,
    scientificRating: 8.5,
    sleepEffectiveness: 8.7,
    focusEffectiveness: 8.3
  },
  {
    id: 'rain_thunderstorm',
    filename: 'thunderstorm.mp3',
    category: 'Rain',
    title: {
      en: 'Thunderstorm',
      zh: '雷雨交加'
    },
    description: {
      en: 'Dramatic thunderstorm with rain and lightning',
      zh: '戏剧性雷雨，伴随闪电'
    },
    tags: ['thunder', 'lightning', 'dramatic'],
    duration: 3600,
    scientificRating: 8.3,
    sleepEffectiveness: 7.8,
    focusEffectiveness: 7.0
  },
  {
    id: 'rain_puddles',
    filename: 'rain_puddles.mp3',
    category: 'Rain',
    title: {
      en: 'Rain Puddles',
      zh: '雨水积潭'
    },
    description: {
      en: 'Rain creating and filling puddles',
      zh: '雨水形成和填满水潭'
    },
    tags: ['puddles', 'splashing', 'urban'],
    duration: 3600,
    scientificRating: 8.1,
    sleepEffectiveness: 8.2,
    focusEffectiveness: 7.8
  },

  // Nature Category (12 files)
  {
    id: 'nature_forest_birds',
    filename: 'forest_birds.mp3',
    category: 'Nature',
    title: {
      en: 'Forest Birds',
      zh: '森林鸟鸣'
    },
    description: {
      en: 'Peaceful bird songs in a morning forest',
      zh: '清晨森林中宁静的鸟儿歌声'
    },
    tags: ['birds', 'forest', 'morning'],
    duration: 3600,
    scientificRating: 8.8,
    sleepEffectiveness: 8.5,
    focusEffectiveness: 9.0
  },
  {
    id: 'nature_ocean_waves',
    filename: 'ocean_waves.mp3',
    category: 'Nature',
    title: {
      en: 'Ocean Waves',
      zh: '海浪声'
    },
    description: {
      en: 'Rhythmic ocean waves on the shore',
      zh: '海岸上有节奏的海浪声'
    },
    tags: ['ocean', 'waves', 'rhythmic'],
    duration: 3600,
    scientificRating: 9.0,
    sleepEffectiveness: 9.1,
    focusEffectiveness: 8.7
  },
  {
    id: 'nature_river_stream',
    filename: 'river_stream.mp3',
    category: 'Nature',
    title: {
      en: 'River Stream',
      zh: '溪流声'
    },
    description: {
      en: 'Gentle flowing river stream',
      zh: '轻柔流淌的河流'
    },
    tags: ['river', 'flowing', 'gentle'],
    duration: 3600,
    scientificRating: 8.9,
    sleepEffectiveness: 8.8,
    focusEffectiveness: 8.9
  },
  {
    id: 'nature_wind_trees',
    filename: 'wind_in_trees.mp3',
    category: 'Nature',
    title: {
      en: 'Wind in Trees',
      zh: '树林风声'
    },
    description: {
      en: 'Gentle wind rustling through tree leaves',
      zh: '微风轻拂树叶的沙沙声'
    },
    tags: ['wind', 'trees', 'rustling'],
    duration: 3600,
    scientificRating: 8.6,
    sleepEffectiveness: 8.4,
    focusEffectiveness: 8.8
  },

  // Noise Category (3 files)
  {
    id: 'noise_white',
    filename: 'white_noise.mp3',
    category: 'Noise',
    title: {
      en: 'White Noise',
      zh: '白噪音'
    },
    description: {
      en: 'Pure white noise for concentration and sleep',
      zh: '纯净白噪音，用于专注和睡眠'
    },
    tags: ['white', 'pure', 'concentration'],
    duration: 3600,
    scientificRating: 8.5,
    sleepEffectiveness: 8.8,
    focusEffectiveness: 9.2
  },
  {
    id: 'noise_pink',
    filename: 'pink_noise.mp3',
    category: 'Noise',
    title: {
      en: 'Pink Noise',
      zh: '粉噪音'
    },
    description: {
      en: 'Balanced pink noise for better sleep',
      zh: '平衡的粉噪音，改善睡眠质量'
    },
    tags: ['pink', 'balanced', 'sleep'],
    duration: 3600,
    scientificRating: 8.7,
    sleepEffectiveness: 9.0,
    focusEffectiveness: 8.9
  },
  {
    id: 'noise_brown',
    filename: 'brown_noise.mp3',
    category: 'Noise',
    title: {
      en: 'Brown Noise',
      zh: '棕噪音'
    },
    description: {
      en: 'Deep brown noise for relaxation',
      zh: '深沉的棕噪音，用于放松'
    },
    tags: ['brown', 'deep', 'relaxation'],
    duration: 3600,
    scientificRating: 8.3,
    sleepEffectiveness: 8.5,
    focusEffectiveness: 8.6
  },

  // Animals Category (16 files)
  {
    id: 'animals_cat_purring',
    filename: 'cat_purring.mp3',
    category: 'Animals',
    title: {
      en: 'Cat Purring',
      zh: '猫咪呼噜声'
    },
    description: {
      en: 'Soothing cat purring sounds',
      zh: '舒缓的猫咪呼噜声'
    },
    tags: ['cat', 'purring', 'soothing'],
    duration: 3600,
    scientificRating: 8.4,
    sleepEffectiveness: 8.6,
    focusEffectiveness: 7.8
  },
  {
    id: 'animals_crickets',
    filename: 'crickets.mp3',
    category: 'Animals',
    title: {
      en: 'Crickets',
      zh: '蟋蟀声'
    },
    description: {
      en: 'Evening cricket sounds',
      zh: '夜晚蟋蟀的鸣叫声'
    },
    tags: ['crickets', 'evening', 'natural'],
    duration: 3600,
    scientificRating: 8.2,
    sleepEffectiveness: 8.3,
    focusEffectiveness: 7.9
  }
];

// 按分类分组的音频数据
export const audioByCategory = audioData.reduce((acc, audio) => {
  if (!acc[audio.category]) {
    acc[audio.category] = [];
  }
  acc[audio.category].push(audio);
  return acc;
}, {} as Record<string, MultilingualAudioItem[]>);

// 获取所有分类
export const categories = Object.keys(audioByCategory).sort();

// 获取分类统计
export const categoryStats = categories.map(category => ({
  name: category,
  count: audioByCategory[category].length,
  avgRating: audioByCategory[category].reduce((sum, audio) => sum + (audio.scientificRating || 0), 0) / audioByCategory[category].length
}));
