import { MultilingualAudioItem } from '@/types/audio';

export const audioData: MultilingualAudioItem[] = [
  // Rain Category (8 files)
  {
    id: 'rain_light_rain',
    filename: 'light-rain.mp3',
    category: 'Rain',
    title: {
      en: 'Light Rain',
      zh: '小雨'
    },
    description: {
      en: 'Soft, gentle rain falling steadily for relaxation',
      zh: '轻柔细雨，持续降落，带来放松感受'
    },
    tags: ['gentle', 'soft', 'continuous'],
    duration: 3600, // 1 hour
    scientificRating: 9.2,
    sleepEffectiveness: 9.5,
    focusEffectiveness: 8.8
  },
  {
    id: 'rain_heavy_rain',
    filename: 'heavy-rain.mp3',
    category: 'Rain',
    title: {
      en: 'Heavy Rain',
      zh: '大雨'
    },
    description: {
      en: 'Intense rainfall with thunder for deep relaxation',
      zh: '强烈降雨伴随雷声，深度放松'
    },
    tags: ['intense', 'thunder', 'powerful'],
    duration: 3600,
    scientificRating: 8.9,
    sleepEffectiveness: 9.0,
    focusEffectiveness: 7.5
  },
  {
    id: 'rain_on_car_roof',
    filename: 'rain-on-car-roof.mp3',
    category: 'Rain',
    title: {
      en: 'Rain on Car Roof',
      zh: '车顶雨声'
    },
    description: {
      en: 'Rhythmic rain drops hitting the car roof',
      zh: '有节奏的雨滴敲击车顶声'
    },
    tags: ['rhythmic', 'cozy', 'car'],
    duration: 3600,
    scientificRating: 9.0,
    sleepEffectiveness: 9.2,
    focusEffectiveness: 8.5
  },
  {
    id: 'rain_on_leaves',
    filename: 'rain-on-leaves.mp3',
    category: 'Rain',
    title: {
      en: 'Rain on Leaves',
      zh: '雨打叶子'
    },
    description: {
      en: 'Rain drops gently hitting leaves in the forest',
      zh: '雨滴轻柔地敲击森林中的叶子'
    },
    tags: ['gentle', 'natural', 'forest'],
    duration: 3600,
    scientificRating: 8.7,
    sleepEffectiveness: 8.8,
    focusEffectiveness: 8.0
  },
  {
    id: 'rain_on_tent',
    filename: 'rain-on-tent.mp3',
    category: 'Rain',
    title: {
      en: 'Rain on Tent',
      zh: '帐篷雨声'
    },
    description: {
      en: 'Rain falling on tent fabric, cozy camping sound',
      zh: '雨水敲击帐篷布料，舒适的露营声音'
    },
    tags: ['tent', 'camping', 'cozy'],
    duration: 3600,
    scientificRating: 9.1,
    sleepEffectiveness: 9.3,
    focusEffectiveness: 8.9
  },
  {
    id: 'rain_on_umbrella',
    filename: 'rain-on-umbrella.mp3',
    category: 'Rain',
    title: {
      en: 'Rain on Umbrella',
      zh: '雨打雨伞'
    },
    description: {
      en: 'Rain drops hitting umbrella surface',
      zh: '雨滴敲击雨伞表面'
    },
    tags: ['umbrella', 'rhythmic', 'close'],
    duration: 3600,
    scientificRating: 8.5,
    sleepEffectiveness: 8.7,
    focusEffectiveness: 8.3
  },
  {
    id: 'rain_on_window',
    filename: 'rain-on-window.mp3',
    category: 'Rain',
    title: {
      en: 'Rain on Window',
      zh: '雨打窗户'
    },
    description: {
      en: 'Rain drops hitting window glass',
      zh: '雨滴敲击窗玻璃'
    },
    tags: ['window', 'indoor', 'peaceful'],
    duration: 3600,
    scientificRating: 8.3,
    sleepEffectiveness: 7.8,
    focusEffectiveness: 7.0
  },
  {
    id: 'thunder',
    filename: 'thunder.mp3',
    category: 'Rain',
    title: {
      en: 'Thunder',
      zh: '雷声'
    },
    description: {
      en: 'Deep thunder sounds for dramatic atmosphere',
      zh: '深沉雷声，营造戏剧性氛围'
    },
    tags: ['thunder', 'dramatic', 'powerful'],
    duration: 3600,
    scientificRating: 8.1,
    sleepEffectiveness: 8.2,
    focusEffectiveness: 7.8
  },

  // Nature Category (12 files)
  {
    id: 'nature_forest_birds',
    filename: 'forest_birds.mp3',
    category: 'Nature',
    title: {
      en: 'Forest Birds',
      zh: '森林鸟鸣'
    },
    description: {
      en: 'Peaceful bird songs in a morning forest',
      zh: '清晨森林中宁静的鸟儿歌声'
    },
    tags: ['birds', 'forest', 'morning'],
    duration: 3600,
    scientificRating: 8.8,
    sleepEffectiveness: 8.5,
    focusEffectiveness: 9.0
  },
  {
    id: 'nature_ocean_waves',
    filename: 'ocean_waves.mp3',
    category: 'Nature',
    title: {
      en: 'Ocean Waves',
      zh: '海浪声'
    },
    description: {
      en: 'Rhythmic ocean waves on the shore',
      zh: '海岸上有节奏的海浪声'
    },
    tags: ['ocean', 'waves', 'rhythmic'],
    duration: 3600,
    scientificRating: 9.0,
    sleepEffectiveness: 9.1,
    focusEffectiveness: 8.7
  },
  {
    id: 'nature_river_stream',
    filename: 'river_stream.mp3',
    category: 'Nature',
    title: {
      en: 'River Stream',
      zh: '溪流声'
    },
    description: {
      en: 'Gentle flowing river stream',
      zh: '轻柔流淌的河流'
    },
    tags: ['river', 'flowing', 'gentle'],
    duration: 3600,
    scientificRating: 8.9,
    sleepEffectiveness: 8.8,
    focusEffectiveness: 8.9
  },
  {
    id: 'nature_wind_trees',
    filename: 'wind_in_trees.mp3',
    category: 'Nature',
    title: {
      en: 'Wind in Trees',
      zh: '树林风声'
    },
    description: {
      en: 'Gentle wind rustling through tree leaves',
      zh: '微风轻拂树叶的沙沙声'
    },
    tags: ['wind', 'trees', 'rustling'],
    duration: 3600,
    scientificRating: 8.6,
    sleepEffectiveness: 8.4,
    focusEffectiveness: 8.8
  },

  // Noise Category (3 files)
  {
    id: 'noise_white',
    filename: 'white_noise.mp3',
    category: 'Noise',
    title: {
      en: 'White Noise',
      zh: '白噪音'
    },
    description: {
      en: 'Pure white noise for concentration and sleep',
      zh: '纯净白噪音，用于专注和睡眠'
    },
    tags: ['white', 'pure', 'concentration'],
    duration: 3600,
    scientificRating: 8.5,
    sleepEffectiveness: 8.8,
    focusEffectiveness: 9.2
  },
  {
    id: 'noise_pink',
    filename: 'pink_noise.mp3',
    category: 'Noise',
    title: {
      en: 'Pink Noise',
      zh: '粉噪音'
    },
    description: {
      en: 'Balanced pink noise for better sleep',
      zh: '平衡的粉噪音，改善睡眠质量'
    },
    tags: ['pink', 'balanced', 'sleep'],
    duration: 3600,
    scientificRating: 8.7,
    sleepEffectiveness: 9.0,
    focusEffectiveness: 8.9
  },
  {
    id: 'noise_brown',
    filename: 'brown_noise.mp3',
    category: 'Noise',
    title: {
      en: 'Brown Noise',
      zh: '棕噪音'
    },
    description: {
      en: 'Deep brown noise for relaxation',
      zh: '深沉的棕噪音，用于放松'
    },
    tags: ['brown', 'deep', 'relaxation'],
    duration: 3600,
    scientificRating: 8.3,
    sleepEffectiveness: 8.5,
    focusEffectiveness: 8.6
  },

  // Animals Category (16 files)
  {
    id: 'animals_cat_purring',
    filename: 'cat_purring.mp3',
    category: 'Animals',
    title: {
      en: 'Cat Purring',
      zh: '猫咪呼噜声'
    },
    description: {
      en: 'Soothing cat purring sounds',
      zh: '舒缓的猫咪呼噜声'
    },
    tags: ['cat', 'purring', 'soothing'],
    duration: 3600,
    scientificRating: 8.4,
    sleepEffectiveness: 8.6,
    focusEffectiveness: 7.8
  },
  {
    id: 'animals_crickets',
    filename: 'crickets.mp3',
    category: 'Animals',
    title: {
      en: 'Crickets',
      zh: '蟋蟀声'
    },
    description: {
      en: 'Evening cricket sounds',
      zh: '夜晚蟋蟀的鸣叫声'
    },
    tags: ['crickets', 'evening', 'natural'],
    duration: 3600,
    scientificRating: 8.2,
    sleepEffectiveness: 8.3,
    focusEffectiveness: 7.9
  },

  // Things Category (15 files)
  {
    id: 'things_fireplace',
    filename: 'fireplace.mp3',
    category: 'Things',
    title: {
      en: 'Fireplace',
      zh: '壁炉声'
    },
    description: {
      en: 'Crackling fireplace sounds for cozy atmosphere',
      zh: '噼啪作响的壁炉声，营造温馨氛围'
    },
    tags: ['fireplace', 'crackling', 'cozy'],
    duration: 3600,
    scientificRating: 8.7,
    sleepEffectiveness: 8.9,
    focusEffectiveness: 8.5
  },
  {
    id: 'things_clock_ticking',
    filename: 'clock_ticking.mp3',
    category: 'Things',
    title: {
      en: 'Clock Ticking',
      zh: '时钟滴答声'
    },
    description: {
      en: 'Rhythmic clock ticking for focus',
      zh: '有节奏的时钟滴答声，帮助专注'
    },
    tags: ['clock', 'ticking', 'rhythmic'],
    duration: 3600,
    scientificRating: 7.8,
    sleepEffectiveness: 7.5,
    focusEffectiveness: 8.2
  },
  {
    id: 'things_washing_machine',
    filename: 'washing_machine.mp3',
    category: 'Things',
    title: {
      en: 'Washing Machine',
      zh: '洗衣机声'
    },
    description: {
      en: 'Gentle washing machine hum',
      zh: '轻柔的洗衣机嗡嗡声'
    },
    tags: ['washing', 'machine', 'hum'],
    duration: 3600,
    scientificRating: 8.1,
    sleepEffectiveness: 8.3,
    focusEffectiveness: 7.9
  },
  {
    id: 'things_fan',
    filename: 'fan.mp3',
    category: 'Things',
    title: {
      en: 'Electric Fan',
      zh: '电风扇声'
    },
    description: {
      en: 'Steady electric fan white noise',
      zh: '稳定的电风扇白噪音'
    },
    tags: ['fan', 'electric', 'steady'],
    duration: 3600,
    scientificRating: 8.4,
    sleepEffectiveness: 8.6,
    focusEffectiveness: 8.8
  },
  {
    id: 'things_air_conditioner',
    filename: 'air_conditioner.mp3',
    category: 'Things',
    title: {
      en: 'Air Conditioner',
      zh: '空调声'
    },
    description: {
      en: 'Air conditioner humming sound',
      zh: '空调嗡嗡声'
    },
    tags: ['air', 'conditioner', 'humming'],
    duration: 3600,
    scientificRating: 8.0,
    sleepEffectiveness: 8.2,
    focusEffectiveness: 8.1
  },

  // Transport Category (6 files)
  {
    id: 'transport_train',
    filename: 'train.mp3',
    category: 'Transport',
    title: {
      en: 'Train Journey',
      zh: '火车行驶声'
    },
    description: {
      en: 'Rhythmic train sounds on tracks',
      zh: '火车在轨道上有节奏的行驶声'
    },
    tags: ['train', 'tracks', 'rhythmic'],
    duration: 3600,
    scientificRating: 8.6,
    sleepEffectiveness: 8.8,
    focusEffectiveness: 8.4
  },
  {
    id: 'transport_airplane',
    filename: 'airplane.mp3',
    category: 'Transport',
    title: {
      en: 'Airplane Cabin',
      zh: '飞机客舱声'
    },
    description: {
      en: 'Airplane cabin ambient noise',
      zh: '飞机客舱环境噪音'
    },
    tags: ['airplane', 'cabin', 'ambient'],
    duration: 3600,
    scientificRating: 8.3,
    sleepEffectiveness: 8.5,
    focusEffectiveness: 8.0
  },
  {
    id: 'transport_car_driving',
    filename: 'car_driving.mp3',
    category: 'Transport',
    title: {
      en: 'Car Driving',
      zh: '汽车行驶声'
    },
    description: {
      en: 'Gentle car driving on highway',
      zh: '汽车在高速公路上平稳行驶'
    },
    tags: ['car', 'driving', 'highway'],
    duration: 3600,
    scientificRating: 7.9,
    sleepEffectiveness: 8.1,
    focusEffectiveness: 7.7
  },

  // Urban Category (7 files)
  {
    id: 'urban_city_traffic',
    filename: 'city_traffic.mp3',
    category: 'Urban',
    title: {
      en: 'City Traffic',
      zh: '城市交通声'
    },
    description: {
      en: 'Distant city traffic ambiance',
      zh: '远处城市交通环境音'
    },
    tags: ['city', 'traffic', 'distant'],
    duration: 3600,
    scientificRating: 7.5,
    sleepEffectiveness: 7.2,
    focusEffectiveness: 7.8
  },
  {
    id: 'urban_construction',
    filename: 'construction.mp3',
    category: 'Urban',
    title: {
      en: 'Construction Site',
      zh: '建筑工地声'
    },
    description: {
      en: 'Distant construction site sounds',
      zh: '远处建筑工地的声音'
    },
    tags: ['construction', 'site', 'distant'],
    duration: 3600,
    scientificRating: 6.8,
    sleepEffectiveness: 6.5,
    focusEffectiveness: 7.0
  },

  // Places Category (6 files)
  {
    id: 'places_coffee_shop',
    filename: 'coffee_shop.mp3',
    category: 'Places',
    title: {
      en: 'Coffee Shop',
      zh: '咖啡店环境音'
    },
    description: {
      en: 'Cozy coffee shop atmosphere',
      zh: '温馨咖啡店氛围'
    },
    tags: ['coffee', 'shop', 'cozy'],
    duration: 3600,
    scientificRating: 8.2,
    sleepEffectiveness: 7.8,
    focusEffectiveness: 8.7
  },
  {
    id: 'places_library',
    filename: 'library.mp3',
    category: 'Places',
    title: {
      en: 'Library',
      zh: '图书馆环境音'
    },
    description: {
      en: 'Quiet library atmosphere',
      zh: '安静的图书馆氛围'
    },
    tags: ['library', 'quiet', 'study'],
    duration: 3600,
    scientificRating: 8.5,
    sleepEffectiveness: 8.0,
    focusEffectiveness: 9.1
  },
  {
    id: 'places_restaurant',
    filename: 'restaurant.mp3',
    category: 'Places',
    title: {
      en: 'Restaurant',
      zh: '餐厅环境音'
    },
    description: {
      en: 'Busy restaurant ambiance',
      zh: '繁忙餐厅的环境音'
    },
    tags: ['restaurant', 'busy', 'social'],
    duration: 3600,
    scientificRating: 7.6,
    sleepEffectiveness: 7.0,
    focusEffectiveness: 7.4
  }
];

// 按分类分组的音频数据
export const audioByCategory = audioData.reduce((acc, audio) => {
  if (!acc[audio.category]) {
    acc[audio.category] = [];
  }
  acc[audio.category].push(audio);
  return acc;
}, {} as Record<string, MultilingualAudioItem[]>);

// 获取所有分类
export const categories = Object.keys(audioByCategory).sort();

// 获取分类统计
export const categoryStats = categories.map(category => ({
  name: category,
  count: audioByCategory[category].length,
  avgRating: audioByCategory[category].reduce((sum, audio) => sum + (audio.scientificRating || 0), 0) / audioByCategory[category].length
}));
