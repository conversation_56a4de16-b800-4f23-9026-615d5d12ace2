{"name": "noisesleep-web", "version": "1.0.0", "description": "Sleep Well / 睡个好觉 - Science-Based Sleep Audio Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "next-intl": "^3.4.0", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "zustand": "^4.4.7", "howler": "^2.2.4", "@types/howler": "^2.2.11", "framer-motion": "^10.16.16", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@tailwindcss/typography": "^0.5.10", "cssnano": "^6.0.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["sleep", "audio", "white-noise", "relaxation", "multilingual", "next.js", "typescript"], "author": "NoiseSleep Team", "license": "MIT"}