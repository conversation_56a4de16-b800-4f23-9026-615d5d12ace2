{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/store/*": ["./src/store/*"], "@/types/*": ["./src/types/*"], "@/i18n/*": ["./src/i18n/*"], "@/utils/*": ["./src/utils/*"]}, "forceConsistentCasingInFileNames": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "out", "dist"]}