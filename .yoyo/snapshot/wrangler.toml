# Cloudflare Pages配置文件
# NoiseSleep项目 - Sleep Well/睡个好觉

name = "noisesleep"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Pages项目配置
[env.production]
name = "noisesleep"
route = "noisesleep.com/*"
zone_name = "noisesleep.com"

[env.staging]
name = "noisesleep-staging"
route = "staging.noisesleep.com/*"

# 构建配置
[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

[build.upload]
format = "modules"
dir = ".next"
main = "./server.js"
rules = [
  { type = "ESModule", globs = ["**/*.js"] },
  { type = "CompiledWasm", globs = ["**/*.wasm"] }
]

# 环境变量
[vars]
NODE_ENV = "production"
NEXT_PUBLIC_SITE_URL = "https://noisesleep.com"
NEXT_PUBLIC_CDN_URL = "https://cdn.noisesleep.com"

# R2存储桶绑定（音频文件）
[[r2_buckets]]
binding = "AUDIO_BUCKET"
bucket_name = "noisesleep-audio"
preview_bucket_name = "noisesleep-audio-preview"

# KV存储绑定（缓存和用户数据）
[[kv_namespaces]]
binding = "CACHE_KV"
id = "your-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"

# D1数据库绑定（用户偏好和统计）
[[d1_databases]]
binding = "DB"
database_name = "noisesleep-db"
database_id = "your-database-id"

# 自定义域名配置
[env.production.route]
pattern = "noisesleep.com/*"
custom_domain = true

[env.production.route]
pattern = "www.noisesleep.com/*"
custom_domain = true

# 重定向规则
[[redirects]]
from = "https://www.noisesleep.com/*"
to = "https://noisesleep.com/:splat"
status = 301

[[redirects]]
from = "https://noisesleep.com/sounds"
to = "https://noisesleep.com/sounds/rain"
status = 302

# 头部配置
[[headers]]
for = "/*"
[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
Referrer-Policy = "strict-origin-when-cross-origin"
Permissions-Policy = "microphone=(), camera=(), geolocation=()"

[[headers]]
for = "/sounds/*"
[headers.values]
Cache-Control = "public, max-age=31536000, immutable"
Access-Control-Allow-Origin = "https://noisesleep.com"

[[headers]]
for = "/_next/static/*"
[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/api/*"
[headers.values]
Cache-Control = "no-cache, no-store, must-revalidate"

# 音频文件特殊处理
[[headers]]
for = "*.mp3"
[headers.values]
Content-Type = "audio/mpeg"
Cache-Control = "public, max-age=2592000" # 30天缓存
Accept-Ranges = "bytes"

[[headers]]
for = "*.wav"
[headers.values]
Content-Type = "audio/wav"
Cache-Control = "public, max-age=2592000"
Accept-Ranges = "bytes"

# 多语言路由支持
[[headers]]
for = "/zh/*"
[headers.values]
Content-Language = "zh-CN"
Vary = "Accept-Language"

[[headers]]
for = "/"
[headers.values]
Content-Language = "en"
Vary = "Accept-Language"

# 安全配置
[env.production.vars]
CSP_POLICY = "default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; media-src 'self' blob: https://cdn.noisesleep.com; connect-src 'self' https://www.google-analytics.com;"
