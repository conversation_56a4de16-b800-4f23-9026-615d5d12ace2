# 🚀 NoiseSleep Cloudflare部署指南

本文档详细说明了如何将NoiseSleep项目部署到Cloudflare Pages，包括音频文件CDN配置、多语言路由设置和性能优化。

## 📋 部署前准备

### 1. Cloudflare账户设置

1. **注册Cloudflare账户**
   - 访问 [cloudflare.com](https://cloudflare.com) 注册账户
   - 验证邮箱并完成账户设置

2. **获取API令牌**
   ```bash
   # 在Cloudflare Dashboard中：
   # 1. 点击右上角用户头像 → "My Profile"
   # 2. 选择 "API Tokens" 标签
   # 3. 点击 "Create Token"
   # 4. 选择 "Custom token" 模板
   # 5. 设置权限：
   #    - Account: Cloudflare Pages:Edit
   #    - Zone: Zone:Read, DNS:Edit
   #    - Zone Resources: Include All zones
   ```

3. **域名配置**
   - 将域名 `noisesleep.com` 添加到Cloudflare
   - 更新域名服务器指向Cloudflare
   - 等待DNS传播完成（通常24-48小时）

### 2. 环境变量配置

创建 `.env.local` 文件：

```bash
# 复制示例文件
cp .env.example .env.local

# 编辑环境变量
nano .env.local
```

必需的环境变量：
```bash
# Cloudflare配置
CLOUDFLARE_API_TOKEN=your_api_token_here
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
CLOUDFLARE_ZONE_ID=your_zone_id_here

# R2存储配置
R2_BUCKET_NAME=noisesleep-audio
R2_ACCESS_KEY_ID=your_r2_access_key
R2_SECRET_ACCESS_KEY=your_r2_secret_key
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com

# 网站配置
NEXT_PUBLIC_SITE_URL=https://noisesleep.com
NEXT_PUBLIC_CDN_URL=https://cdn.noisesleep.com
```

### 3. 依赖安装

```bash
# 安装项目依赖
npm install

# 安装Wrangler CLI（全局）
npm install -g wrangler

# 验证安装
wrangler --version
```

## 🏗️ 部署步骤

### 方法一：自动化部署脚本

```bash
# 赋予执行权限
chmod +x scripts/deploy.sh

# 部署到生产环境
./scripts/deploy.sh production

# 部署到测试环境
./scripts/deploy.sh staging

# 跳过测试直接部署
./scripts/deploy.sh production true
```

### 方法二：手动部署

#### 1. 构建项目
```bash
# 设置环境变量
export NODE_ENV=production
export NEXT_PUBLIC_SITE_URL=https://noisesleep.com

# 安装依赖
npm ci

# 运行测试
npm run type-check
npm run lint

# 构建项目
npm run build
```

#### 2. 上传音频文件
```bash
# 运行音频上传脚本
node scripts/upload-audio.js
```

#### 3. 部署到Cloudflare Pages
```bash
# 认证Wrangler
wrangler auth login

# 部署项目
wrangler pages deploy .next --project-name=noisesleep --compatibility-date=2024-01-01
```

## 🎵 音频文件CDN配置

### 1. R2存储桶设置

```bash
# 创建R2存储桶
wrangler r2 bucket create noisesleep-audio

# 设置CORS策略
wrangler r2 bucket cors put noisesleep-audio --file=cors-policy.json
```

`cors-policy.json` 内容：
```json
[
  {
    "AllowedOrigins": ["https://noisesleep.com"],
    "AllowedMethods": ["GET", "HEAD"],
    "AllowedHeaders": ["*"],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3600
  }
]
```

### 2. 音频文件优化

音频上传脚本会自动：
- 转换为多种格式（MP3, OGG, WebM）
- 标准化音量（-16 LUFS）
- 应用降噪滤波器
- 生成多种比特率版本

### 3. CDN缓存策略

- **音频文件**: 30天缓存 (`max-age=2592000`)
- **静态资源**: 1年缓存 (`max-age=31536000`)
- **API响应**: 无缓存 (`no-cache`)

## 🌍 多语言路由配置

### 1. 域名结构
- 英文版本: `https://noisesleep.com/`
- 中文版本: `https://noisesleep.com/zh/`

### 2. 重定向规则
```
# 主要重定向
www.noisesleep.com → noisesleep.com
/sounds → /sounds/rain
/zh/sounds → /zh/sounds/rain
```

### 3. 语言检测
- 基于 `Accept-Language` 头部自动检测
- 用户可手动切换语言
- 语言偏好保存在本地存储

## ⚡ 性能优化配置

### 1. Core Web Vitals优化

- **LCP优化**: 预加载关键音频文件
- **FID优化**: 代码分割和懒加载
- **CLS优化**: 固定尺寸的音频播放器

### 2. 缓存策略

```
# 静态资源缓存
/_next/static/* → 1年缓存
/sounds/* → 30天缓存
/api/* → 无缓存
```

### 3. 压缩配置

- Gzip压缩所有文本资源
- Brotli压缩（Cloudflare自动启用）
- 音频文件使用最优压缩设置

## 🔧 故障排除

### 常见问题

1. **部署失败**
   ```bash
   # 检查API令牌权限
   wrangler whoami
   
   # 检查构建输出
   ls -la .next/
   
   # 查看详细错误
   wrangler pages deploy .next --verbose
   ```

2. **音频文件无法播放**
   ```bash
   # 检查R2存储桶
   wrangler r2 bucket list
   
   # 检查文件上传
   wrangler r2 object list noisesleep-audio
   
   # 测试文件访问
   curl -I https://cdn.noisesleep.com/sounds/rain/light-rain.mp3
   ```

3. **多语言路由问题**
   ```bash
   # 检查重定向规则
   curl -I https://noisesleep.com/zh
   
   # 验证语言检测
   curl -H "Accept-Language: zh-CN" https://noisesleep.com/
   ```

### 回滚策略

```bash
# 查看部署历史
wrangler pages deployment list --project-name=noisesleep

# 回滚到上一个版本
wrangler pages deployment rollback <deployment-id> --project-name=noisesleep
```

## 📊 监控和分析

### 1. Cloudflare Analytics
- 访问 Cloudflare Dashboard
- 查看 Pages 项目分析
- 监控性能指标和错误率

### 2. 自定义监控
```bash
# 健康检查端点
curl https://noisesleep.com/api/health

# 性能监控
curl https://noisesleep.com/api/metrics
```

### 3. 日志查看
```bash
# 查看实时日志
wrangler pages deployment tail --project-name=noisesleep

# 查看错误日志
wrangler pages deployment logs <deployment-id>
```

## 🔒 安全配置

### 1. SSL/TLS设置
- 启用 "Full (strict)" SSL模式
- 配置 HSTS 头部
- 启用 TLS 1.3

### 2. 安全头部
```
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
Permissions-Policy: microphone=(), camera=(), geolocation=()
```

### 3. 访问控制
- 配置 CORS 策略
- 限制 API 访问频率
- 启用 Bot Fight Mode

## 📝 维护指南

### 定期任务

1. **每周**
   - 检查部署状态
   - 监控性能指标
   - 更新依赖包

2. **每月**
   - 审查安全设置
   - 优化缓存策略
   - 分析用户反馈

3. **每季度**
   - 更新SSL证书
   - 审查访问日志
   - 性能基准测试

### 更新流程

```bash
# 1. 更新代码
git pull origin main

# 2. 更新依赖
npm update

# 3. 运行测试
npm run test

# 4. 部署更新
./scripts/deploy.sh production
```

## 📞 支持联系

如遇到部署问题，请：

1. 查看本文档的故障排除部分
2. 检查 [Cloudflare 状态页面](https://www.cloudflarestatus.com/)
3. 联系技术支持团队

---

**最后更新**: 2025年7月1日  
**文档版本**: v1.0  
**适用环境**: Cloudflare Pages + Next.js 14
