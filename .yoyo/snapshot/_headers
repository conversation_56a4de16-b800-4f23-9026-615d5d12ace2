# Cloudflare Pages静态资源头部配置
# NoiseSleep项目优化配置

# 全局安全头部
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: microphone=(), camera=(), geolocation=(), payment=()
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

# 音频文件优化
/sounds/*
  Cache-Control: public, max-age=2592000, immutable
  Access-Control-Allow-Origin: https://noisesleep.com
  Access-Control-Allow-Methods: GET, HEAD, OPTIONS
  Access-Control-Allow-Headers: Range, Content-Range
  Accept-Ranges: bytes
  Vary: Accept-Encoding

# MP3文件特殊处理
*.mp3
  Content-Type: audio/mpeg
  Cache-Control: public, max-age=2592000, immutable
  Accept-Ranges: bytes

# WAV文件处理
*.wav
  Content-Type: audio/wav
  Cache-Control: public, max-age=2592000, immutable
  Accept-Ranges: bytes

# OGG文件处理
*.ogg
  Content-Type: audio/ogg
  Cache-Control: public, max-age=2592000, immutable
  Accept-Ranges: bytes

# WebM音频文件处理
*.webm
  Content-Type: audio/webm
  Cache-Control: public, max-age=2592000, immutable
  Accept-Ranges: bytes

# Next.js静态资源
/_next/static/*
  Cache-Control: public, max-age=31536000, immutable
  Access-Control-Allow-Origin: *

# 图片资源
*.jpg
  Cache-Control: public, max-age=2592000
  Content-Type: image/jpeg

*.jpeg
  Cache-Control: public, max-age=2592000
  Content-Type: image/jpeg

*.png
  Cache-Control: public, max-age=2592000
  Content-Type: image/png

*.webp
  Cache-Control: public, max-age=2592000
  Content-Type: image/webp

*.avif
  Cache-Control: public, max-age=2592000
  Content-Type: image/avif

*.svg
  Cache-Control: public, max-age=2592000
  Content-Type: image/svg+xml

# 字体文件
*.woff
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff
  Access-Control-Allow-Origin: *

*.woff2
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff2
  Access-Control-Allow-Origin: *

*.ttf
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/ttf
  Access-Control-Allow-Origin: *

# CSS和JS文件
*.css
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: text/css

*.js
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: application/javascript

# API路由
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

# 多语言页面
/zh/*
  Content-Language: zh-CN
  Vary: Accept-Language

/
  Content-Language: en
  Vary: Accept-Language

# PWA文件
/manifest.json
  Cache-Control: public, max-age=86400
  Content-Type: application/manifest+json

/sw.js
  Cache-Control: no-cache, no-store, must-revalidate
  Content-Type: application/javascript

# 网站地图和robots
/sitemap.xml
  Cache-Control: public, max-age=86400
  Content-Type: application/xml

/robots.txt
  Cache-Control: public, max-age=86400
  Content-Type: text/plain

# 错误页面
/404.html
  Cache-Control: public, max-age=3600

/500.html
  Cache-Control: public, max-age=3600

# 预加载关键资源
/
  Link: </sounds/rain/light-rain.mp3>; rel=prefetch; as=audio
  Link: </_next/static/css/app.css>; rel=preload; as=style
  Link: <https://fonts.googleapis.com>; rel=preconnect
  Link: <https://fonts.gstatic.com>; rel=preconnect; crossorigin

# 压缩配置
*.html
  Content-Encoding: gzip
  Vary: Accept-Encoding

*.css
  Content-Encoding: gzip
  Vary: Accept-Encoding

*.js
  Content-Encoding: gzip
  Vary: Accept-Encoding

*.json
  Content-Encoding: gzip
  Vary: Accept-Encoding
