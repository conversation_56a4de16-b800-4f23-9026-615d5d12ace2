'use client';

import { useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { clsx } from 'clsx';
import { MultilingualAudioItem } from '@/types/audio';
import { useAudioStore } from '@/store/audioStore';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';
import { SmallPlayButton } from '@/components/AudioPlayer';

interface AudioCardProps {
  audio: MultilingualAudioItem;
  variant?: 'default' | 'compact' | 'detailed';
  showCategory?: boolean;
  showTags?: boolean;
  showDuration?: boolean;
  showDescription?: boolean;
  onPlay?: (audio: MultilingualAudioItem) => void;
  className?: string;
}

export function AudioCard({
  audio,
  variant = 'default',
  showCategory = true,
  showTags = true,
  showDuration = false,
  showDescription = false,
  onPlay,
  className,
}: AudioCardProps) {
  const t = useTranslations('common');
  const locale = useLocale();
  const [imageError, setImageError] = useState(false);
  
  const {
    currentSound,
    playState,
    favorites,
    addToFavorites,
    removeFromFavorites
  } = useAudioStore();

  const {
    play,
    pause,
    isPlaying,
    isLoading,
  } = useAudioPlayer();

  const isCurrentlyPlaying = currentSound?.id === audio.id && isPlaying;
  const isFavorite = favorites.includes(audio.id);

  // 获取本地化文本
  const getLocalizedText = (textObj: Record<string, string>) => {
    return textObj[locale] || textObj.en || Object.values(textObj)[0] || '';
  };

  const title = getLocalizedText(audio.title);
  const description = audio.description ? getLocalizedText(audio.description) : '';

  // 处理播放
  const handlePlay = () => {
    if (isCurrentlyPlaying) {
      pause();
    } else {
      play(audio);
    }
    onPlay?.(audio);
  };

  // 处理收藏
  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isFavorite) {
      removeFromFavorites(audio.id);
    } else {
      addToFavorites(audio.id);
    }
  };

  // 获取音频图标
  const getAudioIcon = () => {
    const iconMap: Record<string, string> = {
      rain: '🌧️',
      nature: '🌿',
      noise: '🔊',
      animals: '🐾',
      things: '🏠',
      transport: '🚗',
      urban: '🏙️',
      places: '📍',
    };
    return iconMap[audio.category.toLowerCase()] || '🎵';
  };

  // 格式化时长
  const formatDuration = (seconds: number) => {
    if (!seconds || seconds <= 0) return '';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 紧凑版本
  if (variant === 'compact') {
    return (
      <div className={clsx(
        'flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg',
        'border border-gray-200 dark:border-gray-700 hover:border-amber-300 dark:hover:border-amber-600',
        'transition-all duration-200 cursor-pointer group',
        isCurrentlyPlaying && 'ring-2 ring-amber-500 border-amber-500',
        className
      )}>
        {/* 播放按钮 */}
        <SmallPlayButton
          isPlaying={isCurrentlyPlaying}
          isLoading={isLoading && currentSound?.id === audio.id}
          onPlay={handlePlay}
          onPause={handlePlay}
        />

        {/* 音频信息 */}
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
            {title}
          </h4>
          {showCategory && (
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {getAudioIcon()} {audio.category}
            </p>
          )}
        </div>

        {/* 收藏按钮 */}
        <button
          onClick={handleToggleFavorite}
          className={clsx(
            'p-1 rounded-full transition-colors opacity-0 group-hover:opacity-100',
            isFavorite && 'opacity-100',
            'hover:bg-gray-100 dark:hover:bg-gray-700',
            isFavorite ? 'text-red-500' : 'text-gray-400'
          )}
          aria-label={isFavorite ? t('removeFromFavorites') : t('addToFavorites')}
        >
          <svg className="w-4 h-4" fill={isFavorite ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>
      </div>
    );
  }

  // 详细版本
  if (variant === 'detailed') {
    return (
      <div className={clsx(
        'bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700',
        'hover:shadow-md hover:border-amber-300 dark:hover:border-amber-600',
        'transition-all duration-200 overflow-hidden group',
        isCurrentlyPlaying && 'ring-2 ring-amber-500 border-amber-500',
        className
      )}>
        {/* 音频封面/图标 */}
        <div className="relative h-32 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20">
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-4xl">{getAudioIcon()}</span>
          </div>
          
          {/* 播放按钮覆盖层 */}
          <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
            <SmallPlayButton
              isPlaying={isCurrentlyPlaying}
              isLoading={isLoading && currentSound?.id === audio.id}
              onPlay={handlePlay}
              onPause={handlePlay}
              variant="primary"
            />
          </div>

          {/* 收藏按钮 */}
          <button
            onClick={handleToggleFavorite}
            className={clsx(
              'absolute top-2 right-2 p-2 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm',
              'transition-all duration-200 opacity-0 group-hover:opacity-100',
              isFavorite && 'opacity-100',
              'hover:bg-white dark:hover:bg-gray-800',
              isFavorite ? 'text-red-500' : 'text-gray-400'
            )}
            aria-label={isFavorite ? t('removeFromFavorites') : t('addToFavorites')}
          >
            <svg className="w-5 h-5" fill={isFavorite ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </button>
        </div>

        {/* 音频信息 */}
        <div className="p-4">
          <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
            {title}
          </h3>
          
          {showDescription && description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
              {description}
            </p>
          )}

          {/* 标签和分类 */}
          <div className="flex flex-wrap gap-2 mb-3">
            {showCategory && (
              <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 rounded-full">
                {getAudioIcon()} {audio.category}
              </span>
            )}
            {showTags && audio.tags?.slice(0, 2).map((tag) => (
              <span key={tag} className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-full">
                {tag}
              </span>
            ))}
          </div>

          {/* 底部信息 */}
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            {showDuration && audio.duration && (
              <span>{formatDuration(audio.duration)}</span>
            )}
            {audio.scientificRating && (
              <div className="flex items-center gap-1">
                <span>⭐</span>
                <span>{audio.scientificRating.toFixed(1)}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // 默认版本
  return (
    <div className={clsx(
      'bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700',
      'hover:shadow-md hover:border-amber-300 dark:hover:border-amber-600',
      'transition-all duration-200 overflow-hidden cursor-pointer group',
      isCurrentlyPlaying && 'ring-2 ring-amber-500 border-amber-500',
      className
    )}
    onClick={handlePlay}
    >
      <div className="p-4">
        {/* 头部 */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20 rounded-lg flex items-center justify-center">
              <span className="text-lg">{getAudioIcon()}</span>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                {title}
              </h3>
              {showCategory && (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {audio.category}
                </p>
              )}
            </div>
          </div>

          {/* 播放状态指示器 */}
          {isCurrentlyPlaying && (
            <div className="flex items-center gap-1">
              <div className="w-1 h-3 bg-amber-500 rounded-full animate-pulse"></div>
              <div className="w-1 h-4 bg-amber-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-1 h-3 bg-amber-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
            </div>
          )}
        </div>

        {/* 描述 */}
        {showDescription && description && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
            {description}
          </p>
        )}

        {/* 标签 */}
        {showTags && audio.tags && audio.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {audio.tags.slice(0, 3).map((tag) => (
              <span key={tag} className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-full">
                {tag}
              </span>
            ))}
            {audio.tags.length > 3 && (
              <span className="px-2 py-1 text-xs font-medium text-gray-500 dark:text-gray-500">
                +{audio.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <SmallPlayButton
              isPlaying={isCurrentlyPlaying}
              isLoading={isLoading && currentSound?.id === audio.id}
              onPlay={(e) => {
                e?.stopPropagation();
                handlePlay();
              }}
              onPause={(e) => {
                e?.stopPropagation();
                handlePlay();
              }}
              variant="ghost"
            />
            
            {showDuration && audio.duration && (
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {formatDuration(audio.duration)}
              </span>
            )}
          </div>

          <div className="flex items-center gap-2">
            {audio.scientificRating && (
              <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                <span>⭐</span>
                <span>{audio.scientificRating.toFixed(1)}</span>
              </div>
            )}
            
            <button
              onClick={handleToggleFavorite}
              className={clsx(
                'p-1 rounded-full transition-colors',
                'hover:bg-gray-100 dark:hover:bg-gray-700',
                isFavorite ? 'text-red-500' : 'text-gray-400'
              )}
              aria-label={isFavorite ? t('removeFromFavorites') : t('addToFavorites')}
            >
              <svg className="w-4 h-4" fill={isFavorite ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
