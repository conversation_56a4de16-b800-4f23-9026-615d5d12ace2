'use client';

import { useTranslations } from 'next-intl';
import { clsx } from 'clsx';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';
import { useAudioStore } from '@/store/audioStore';
import { MultilingualAudioItem } from '@/types/audio';
import { PlayButton } from './PlayButton';
import { VolumeControl } from './VolumeControl';
import { ProgressBar } from './ProgressBar';

interface AudioPlayerProps {
  sound?: MultilingualAudioItem;
  variant?: 'compact' | 'full' | 'mini';
  showProgress?: boolean;
  showVolume?: boolean;
  showInfo?: boolean;
  className?: string;
}

export function AudioPlayer({
  sound,
  variant = 'full',
  showProgress = true,
  showVolume = true,
  showInfo = true,
  className,
}: AudioPlayerProps) {
  const t = useTranslations('common');
  const { currentSound, favorites, addToFavorites, removeFromFavorites } = useAudioStore();
  
  const {
    play,
    pause,
    stop,
    setVolume,
    setLoop,
    seek,
    isPlaying,
    isPaused,
    isLoading,
    currentTime,
    duration,
    volume,
    isLooping,
    error,
  } = useAudioPlayer();

  // 使用传入的 sound 或当前播放的 sound
  const displaySound = sound || currentSound;
  const isFavorite = displaySound ? favorites.includes(displaySound.id) : false;

  // 播放控制
  const handlePlay = () => {
    if (sound && sound.id !== currentSound?.id) {
      play(sound);
    } else {
      play();
    }
  };

  const handlePause = () => {
    pause();
  };

  const handleStop = () => {
    stop();
  };

  // 收藏切换
  const toggleFavorite = () => {
    if (!displaySound) return;
    
    if (isFavorite) {
      removeFromFavorites(displaySound.id);
    } else {
      addToFavorites(displaySound.id);
    }
  };

  // 循环切换
  const toggleLoop = () => {
    setLoop(!isLooping);
  };

  // 获取音频标题
  const getAudioTitle = (audioItem: MultilingualAudioItem, locale: string = 'en') => {
    return audioItem.title[locale as keyof typeof audioItem.title] || audioItem.title.en;
  };

  // 获取音频描述
  const getAudioDescription = (audioItem: MultilingualAudioItem, locale: string = 'en') => {
    return audioItem.description?.[locale as keyof typeof audioItem.description] || audioItem.description?.en || '';
  };

  if (!displaySound) {
    return (
      <div className={clsx(
        'flex items-center justify-center p-8 text-gray-500 dark:text-gray-400',
        'bg-gray-50 dark:bg-gray-900 rounded-lg border-2 border-dashed border-gray-200 dark:border-gray-700',
        className
      )}>
        <div className="text-center">
          <div className="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600">
            <svg fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
            </svg>
          </div>
          <p className="text-sm">{t('noAudioSelected')}</p>
        </div>
      </div>
    );
  }

  // 紧凑版本
  if (variant === 'compact') {
    return (
      <div className={clsx(
        'flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700',
        className
      )}>
        <PlayButton
          isPlaying={isPlaying && currentSound?.id === displaySound.id}
          isLoading={isLoading}
          onPlay={handlePlay}
          onPause={handlePause}
          size="sm"
        />
        
        {showInfo && (
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
              {getAudioTitle(displaySound)}
            </h4>
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {displaySound.category}
            </p>
          </div>
        )}

        {showVolume && (
          <div className="w-20">
            <VolumeControl
              volume={volume}
              onVolumeChange={setVolume}
              size="sm"
              showIcon={false}
            />
          </div>
        )}
      </div>
    );
  }

  // 迷你版本
  if (variant === 'mini') {
    return (
      <div className={clsx('flex items-center gap-2', className)}>
        <PlayButton
          isPlaying={isPlaying && currentSound?.id === displaySound.id}
          isLoading={isLoading}
          onPlay={handlePlay}
          onPause={handlePause}
          size="sm"
          variant="ghost"
        />
        {showInfo && (
          <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
            {getAudioTitle(displaySound)}
          </span>
        )}
      </div>
    );
  }

  // 完整版本
  return (
    <div className={clsx(
      'bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden',
      className
    )}>
      {/* 错误提示 */}
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}

      {/* 音频信息 */}
      {showInfo && (
        <div className="p-6 pb-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
                {getAudioTitle(displaySound)}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                {getAudioDescription(displaySound)}
              </p>
              <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-500">
                <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full">
                  {displaySound.category}
                </span>
                {displaySound.tags?.map((tag) => (
                  <span key={tag} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
            
            {/* 收藏按钮 */}
            <button
              onClick={toggleFavorite}
              className={clsx(
                'p-2 rounded-full transition-colors',
                'hover:bg-gray-100 dark:hover:bg-gray-700',
                'focus:outline-none focus:ring-2 focus:ring-amber-500',
                isFavorite ? 'text-red-500' : 'text-gray-400'
              )}
              aria-label={isFavorite ? t('removeFromFavorites') : t('addToFavorites')}
            >
              <svg className="w-5 h-5" fill={isFavorite ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* 进度条 */}
      {showProgress && (
        <div className="px-6 pb-4">
          <ProgressBar
            currentTime={currentTime}
            duration={duration}
            onSeek={seek}
            isLoading={isLoading}
            showTime={true}
          />
        </div>
      )}

      {/* 控制面板 */}
      <div className="px-6 pb-6">
        <div className="flex items-center justify-between">
          {/* 左侧控制 */}
          <div className="flex items-center gap-2">
            <button
              onClick={toggleLoop}
              className={clsx(
                'p-2 rounded-full transition-colors',
                'hover:bg-gray-100 dark:hover:bg-gray-700',
                'focus:outline-none focus:ring-2 focus:ring-amber-500',
                isLooping ? 'text-amber-500' : 'text-gray-400'
              )}
              aria-label={isLooping ? t('disableLoop') : t('enableLoop')}
              title={isLooping ? t('disableLoop') : t('enableLoop')}
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M7 7h10v3l4-4-4-4v3H5v6h2V7zm10 10H7v-3l-4 4 4 4v-3h12v-6h-2v4z"/>
              </svg>
            </button>
          </div>

          {/* 中央播放控制 */}
          <div className="flex items-center gap-4">
            <button
              onClick={handleStop}
              disabled={!isPlaying && !isPaused}
              className={clsx(
                'p-2 rounded-full transition-colors',
                'hover:bg-gray-100 dark:hover:bg-gray-700',
                'focus:outline-none focus:ring-2 focus:ring-amber-500',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                'text-gray-600 dark:text-gray-400'
              )}
              aria-label={t('stop')}
              title={t('stop')}
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 6h12v12H6z"/>
              </svg>
            </button>

            <PlayButton
              isPlaying={isPlaying && currentSound?.id === displaySound.id}
              isLoading={isLoading}
              onPlay={handlePlay}
              onPause={handlePause}
              size="lg"
            />
          </div>

          {/* 右侧音量控制 */}
          {showVolume && (
            <div className="w-32">
              <VolumeControl
                volume={volume}
                onVolumeChange={setVolume}
                size="md"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
