interface FeatureCardProps {
  icon: string;
  title: string;
  description: string;
  bgColor?: string;
  className?: string;
}

export default function FeatureCard({
  icon,
  title,
  description,
  bgColor = 'bg-indigo-100',
  className = ''
}: FeatureCardProps) {
  return (
    <div className={`text-center ${className}`}>
      <div className={`w-16 h-16 ${bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}>
        <span className="text-2xl">{icon}</span>
      </div>
      <h4 className="text-xl font-medium text-gray-800 mb-2">{title}</h4>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}
