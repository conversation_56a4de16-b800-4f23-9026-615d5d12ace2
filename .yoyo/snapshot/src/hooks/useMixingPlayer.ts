import { useEffect, useRef, useCallback } from 'react';
import { Howl, Howler } from 'howler';
import { useAudioStore } from '@/store/audioStore';
import { MultilingualAudioItem, MixingChannel } from '@/types/audio';
import { audioData } from '@/data/audioData';

interface MixingPlayerInstance {
  howl: Howl;
  isPlaying: boolean;
  isLoading: boolean;
  error: string | null;
}

interface UseMixingPlayerReturn {
  playChannel: (channelId: string) => void;
  pauseChannel: (channelId: string) => void;
  stopChannel: (channelId: string) => void;
  setChannelVolume: (channelId: string, volume: number) => void;
  muteChannel: (channelId: string) => void;
  unmuteChannel: (channelId: string) => void;
  getChannelState: (channelId: string) => {
    isPlaying: boolean;
    isLoading: boolean;
    error: string | null;
  };
  stopAllChannels: () => void;
  setMasterVolume: (volume: number) => void;
}

export const useMixingPlayer = (): UseMixingPlayerReturn => {
  const playersRef = useRef<Map<string, MixingPlayerInstance>>(new Map());
  
  const {
    mixingChannels,
    masterVolume,
    updateChannelVolume,
    setMasterVolume: setStoreMasterVolume,
  } = useAudioStore();

  // 获取音频文件路径
  const getAudioPath = useCallback((audio: MultilingualAudioItem) => {
    return `/Sounds/${audio.category}/${audio.filename}`;
  }, []);

  // 创建音频播放器实例
  const createPlayer = useCallback((channel: MixingChannel, audio: MultilingualAudioItem) => {
    const audioPath = getAudioPath(audio);
    
    const howl = new Howl({
      src: [audioPath],
      volume: (channel.volume * masterVolume) * (channel.isMuted ? 0 : 1),
      loop: true, // 混音通常需要循环播放
      onload: () => {
        const player = playersRef.current.get(channel.id);
        if (player) {
          player.isLoading = false;
          player.error = null;
        }
      },
      onplay: () => {
        const player = playersRef.current.get(channel.id);
        if (player) {
          player.isPlaying = true;
        }
      },
      onpause: () => {
        const player = playersRef.current.get(channel.id);
        if (player) {
          player.isPlaying = false;
        }
      },
      onstop: () => {
        const player = playersRef.current.get(channel.id);
        if (player) {
          player.isPlaying = false;
        }
      },
      onloaderror: (id, error) => {
        console.error(`音频加载失败 (${channel.id}):`, error);
        const player = playersRef.current.get(channel.id);
        if (player) {
          player.isLoading = false;
          player.error = '音频加载失败';
        }
      },
      onplayerror: (id, error) => {
        console.error(`音频播放失败 (${channel.id}):`, error);
        const player = playersRef.current.get(channel.id);
        if (player) {
          player.isPlaying = false;
          player.error = '音频播放失败';
        }
      },
    });

    const playerInstance: MixingPlayerInstance = {
      howl,
      isPlaying: false,
      isLoading: true,
      error: null,
    };

    playersRef.current.set(channel.id, playerInstance);
    return playerInstance;
  }, [getAudioPath, masterVolume]);

  // 播放频道
  const playChannel = useCallback((channelId: string) => {
    const player = playersRef.current.get(channelId);
    if (player && !player.isLoading && !player.error) {
      player.howl.play();
    }
  }, []);

  // 暂停频道
  const pauseChannel = useCallback((channelId: string) => {
    const player = playersRef.current.get(channelId);
    if (player) {
      player.howl.pause();
    }
  }, []);

  // 停止频道
  const stopChannel = useCallback((channelId: string) => {
    const player = playersRef.current.get(channelId);
    if (player) {
      player.howl.stop();
    }
  }, []);

  // 设置频道音量
  const setChannelVolume = useCallback((channelId: string, volume: number) => {
    const player = playersRef.current.get(channelId);
    const channel = mixingChannels.find(c => c.id === channelId);
    
    if (player && channel) {
      const finalVolume = (volume * masterVolume) * (channel.isMuted ? 0 : 1);
      player.howl.volume(finalVolume);
      updateChannelVolume(channelId, volume);
    }
  }, [mixingChannels, masterVolume, updateChannelVolume]);

  // 静音频道
  const muteChannel = useCallback((channelId: string) => {
    const player = playersRef.current.get(channelId);
    if (player) {
      player.howl.volume(0);
    }
  }, []);

  // 取消静音频道
  const unmuteChannel = useCallback((channelId: string) => {
    const player = playersRef.current.get(channelId);
    const channel = mixingChannels.find(c => c.id === channelId);
    
    if (player && channel) {
      const finalVolume = channel.volume * masterVolume;
      player.howl.volume(finalVolume);
    }
  }, [mixingChannels, masterVolume]);

  // 获取频道状态
  const getChannelState = useCallback((channelId: string) => {
    const player = playersRef.current.get(channelId);
    if (player) {
      return {
        isPlaying: player.isPlaying,
        isLoading: player.isLoading,
        error: player.error,
      };
    }
    return {
      isPlaying: false,
      isLoading: false,
      error: null,
    };
  }, []);

  // 停止所有频道
  const stopAllChannels = useCallback(() => {
    playersRef.current.forEach((player) => {
      player.howl.stop();
    });
  }, []);

  // 设置主音量
  const setMasterVolume = useCallback((volume: number) => {
    setStoreMasterVolume(volume);
    
    // 更新所有频道的音量
    playersRef.current.forEach((player, channelId) => {
      const channel = mixingChannels.find(c => c.id === channelId);
      if (channel) {
        const finalVolume = (channel.volume * volume) * (channel.isMuted ? 0 : 1);
        player.howl.volume(finalVolume);
      }
    });
  }, [mixingChannels, setStoreMasterVolume]);

  // 监听混音频道变化，创建或销毁播放器实例
  useEffect(() => {
    const currentChannelIds = new Set(mixingChannels.map(c => c.id));
    const playerChannelIds = new Set(playersRef.current.keys());

    // 移除不存在的频道
    playerChannelIds.forEach(channelId => {
      if (!currentChannelIds.has(channelId)) {
        const player = playersRef.current.get(channelId);
        if (player) {
          player.howl.unload();
          playersRef.current.delete(channelId);
        }
      }
    });

    // 为新频道创建播放器
    mixingChannels.forEach(channel => {
      if (!playersRef.current.has(channel.id)) {
        const audio = audioData.find(a => a.id === channel.soundId);
        if (audio) {
          createPlayer(channel, audio);
        }
      }
    });
  }, [mixingChannels, createPlayer]);

  // 组件卸载时清理所有播放器
  useEffect(() => {
    return () => {
      playersRef.current.forEach((player) => {
        player.howl.unload();
      });
      playersRef.current.clear();
    };
  }, []);

  return {
    playChannel,
    pauseChannel,
    stopChannel,
    setChannelVolume,
    muteChannel,
    unmuteChannel,
    getChannelState,
    stopAllChannels,
    setMasterVolume,
  };
};
