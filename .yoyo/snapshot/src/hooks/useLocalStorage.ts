import { useState, useEffect, useCallback } from 'react';

type SetValue<T> = T | ((val: T) => T);

interface UseLocalStorageReturn<T> {
  value: T;
  setValue: (value: SetValue<T>) => void;
  removeValue: () => void;
  isLoading: boolean;
  error: string | null;
}

/**
 * 自定义 Hook 用于管理 localStorage
 * @param key - localStorage 的键名
 * @param initialValue - 初始值
 * @returns 包含值、设置函数、删除函数等的对象
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): UseLocalStorageReturn<T> {
  const [storedValue, setStoredValue] = useState<T>(initialValue);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 从 localStorage 读取值
  const readValue = useCallback((): T => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      if (item === null) {
        return initialValue;
      }
      
      // 尝试解析 JSON，如果失败则返回原始字符串
      try {
        return JSON.parse(item);
      } catch {
        // 如果不是 JSON 格式，直接返回字符串值
        return item as unknown as T;
      }
    } catch (error) {
      console.warn(`读取 localStorage 键 "${key}" 时出错:`, error);
      setError(`读取存储数据失败: ${error}`);
      return initialValue;
    }
  }, [initialValue, key]);

  // 设置值到 localStorage
  const setValue = useCallback((value: SetValue<T>) => {
    if (typeof window === 'undefined') {
      console.warn('localStorage 在服务端不可用');
      return;
    }

    try {
      setError(null);
      
      // 计算新值
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // 更新状态
      setStoredValue(valueToStore);
      
      // 保存到 localStorage
      if (valueToStore === undefined || valueToStore === null) {
        window.localStorage.removeItem(key);
      } else {
        const serializedValue = typeof valueToStore === 'string' 
          ? valueToStore 
          : JSON.stringify(valueToStore);
        window.localStorage.setItem(key, serializedValue);
      }

      // 触发自定义事件，通知其他组件
      window.dispatchEvent(new CustomEvent('localStorage-change', {
        detail: { key, value: valueToStore }
      }));
      
    } catch (error) {
      console.error(`设置 localStorage 键 "${key}" 时出错:`, error);
      setError(`保存数据失败: ${error}`);
    }
  }, [key, storedValue]);

  // 删除值
  const removeValue = useCallback(() => {
    if (typeof window === 'undefined') {
      return;
    }

    try {
      setError(null);
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
      
      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('localStorage-change', {
        detail: { key, value: null }
      }));
    } catch (error) {
      console.error(`删除 localStorage 键 "${key}" 时出错:`, error);
      setError(`删除数据失败: ${error}`);
    }
  }, [key, initialValue]);

  // 初始化时读取值
  useEffect(() => {
    setIsLoading(true);
    try {
      const value = readValue();
      setStoredValue(value);
    } catch (error) {
      console.error('初始化 localStorage 值时出错:', error);
    } finally {
      setIsLoading(false);
    }
  }, [readValue]);

  // 监听 localStorage 变化（跨标签页同步）
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          const newValue = JSON.parse(e.newValue);
          setStoredValue(newValue);
        } catch {
          setStoredValue(e.newValue as unknown as T);
        }
      }
    };

    // 监听自定义事件（同一页面内的变化）
    const handleCustomStorageChange = (e: CustomEvent) => {
      if (e.detail.key === key) {
        setStoredValue(e.detail.value ?? initialValue);
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('storage', handleStorageChange);
      window.addEventListener('localStorage-change', handleCustomStorageChange as EventListener);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('storage', handleStorageChange);
        window.removeEventListener('localStorage-change', handleCustomStorageChange as EventListener);
      }
    };
  }, [key, initialValue]);

  return {
    value: storedValue,
    setValue,
    removeValue,
    isLoading,
    error,
  };
}

/**
 * 预定义的常用 localStorage hooks
 */

// 用户偏好设置
export const useUserPreferences = () => {
  return useLocalStorage('noisesleep-user-preferences', {
    volume: 0.7,
    theme: 'auto' as 'light' | 'dark' | 'auto',
    language: 'en' as 'en' | 'zh',
    autoPlay: false,
    showWelcome: true,
  });
};

// 播放历史
export const usePlayHistory = () => {
  return useLocalStorage('noisesleep-play-history', [] as string[]);
};

// 收藏列表
export const useFavorites = () => {
  return useLocalStorage('noisesleep-favorites', [] as string[]);
};
