import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import { MixingBoard } from '@/components/MixingBoard';

interface MixingPageProps {
  params: {
    locale: string;
  };
}

export async function generateMetadata({ params }: { params: { locale: string } }) {
  const t = await getTranslations({ locale: params.locale, namespace: 'metadata' });
  
  return {
    title: t('mixing.title'),
    description: t('mixing.description'),
  };
}

export default function MixingPage({ params }: MixingPageProps) {
  const t = useTranslations('mixing');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面头部 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            {t('pageTitle')}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
            {t('pageDescription')}
          </p>

          {/* 功能介绍 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-8 h-8 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center">
                  <span className="text-lg">🎛️</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                  {t('feature1Title')}
                </h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t('feature1Description')}
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-8 h-8 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center">
                  <span className="text-lg">🔊</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                  {t('feature2Title')}
                </h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t('feature2Description')}
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-8 h-8 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center">
                  <span className="text-lg">⚡</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                  {t('feature3Title')}
                </h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t('feature3Description')}
              </p>
            </div>
          </div>

          {/* MVP 限制提示 */}
          <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-8">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5">
                <svg fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-amber-800 dark:text-amber-200 mb-1">
                  {t('mvpNoticeTitle')}
                </h4>
                <p className="text-sm text-amber-700 dark:text-amber-300">
                  {t('mvpNoticeDescription')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 混音板 */}
        <MixingBoard />

        {/* 使用提示 */}
        <div className="mt-12 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            {t('howToUseTitle')}
          </h2>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                1
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                  {t('step1Title')}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {t('step1Description')}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                2
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                  {t('step2Title')}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {t('step2Description')}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                3
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                  {t('step3Title')}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {t('step3Description')}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                4
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                  {t('step4Title')}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {t('step4Description')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
