import { useTranslations } from 'next-intl';
import { setRequestLocale } from 'next-intl/server';
import Link from 'next/link';

type Props = {
  params: { locale: string };
};

export default function HomePage({ params: { locale } }: Props) {
  // 启用静态渲染优化
  setRequestLocale(locale);
  
  const t = useTranslations('home');
  const tCommon = useTranslations('common');
  const tNav = useTranslations('navigation');

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* 导航栏 */}
      <nav className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {locale === 'zh' ? '睡个好觉' : 'Sleep Well'}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link 
                href={`/${locale}/sounds`}
                className="text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                {tNav('sounds')}
              </Link>
              <Link 
                href={`/${locale}/about`}
                className="text-gray-700 dark:text-gray-300 hover:text-amber-600 dark:hover:text-amber-400 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                {tNav('about')}
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            {t('hero.title')}
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            {t('hero.subtitle')}
          </p>
          <Link
            href={`/${locale}/sounds`}
            className="inline-flex items-center px-8 py-4 bg-amber-500 hover:bg-amber-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            {t('hero.cta')}
            <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>

        {/* Features Section */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-amber-100 dark:bg-amber-900 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {t('features.scientific.title')}
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              {t('features.scientific.description')}
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {t('features.multilingual.title')}
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              {t('features.multilingual.description')}
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {t('features.mixing.title')}
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              {t('features.mixing.description')}
            </p>
          </div>
        </div>

        {/* 音频分类预览 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
            {locale === 'zh' ? '8大音频分类' : '8 Audio Categories'}
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { key: 'rain', icon: '🌧️', count: 8 },
              { key: 'nature', icon: '🌿', count: 12 },
              { key: 'noise', icon: '🔊', count: 3 },
              { key: 'animals', icon: '🐾', count: 16 },
              { key: 'things', icon: '🏠', count: 15 },
              { key: 'transport', icon: '🚗', count: 6 },
              { key: 'urban', icon: '🏙️', count: 7 },
              { key: 'places', icon: '📍', count: 6 }
            ].map((category) => (
              <Link
                key={category.key}
                href={`/${locale}/sounds/${category.key}`}
                className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors text-center"
              >
                <div className="text-2xl mb-2">{category.icon}</div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {locale === 'zh' ? 
                    ['雨声', '自然', '白噪音', '动物', '物品', '交通', '城市', '场所'][
                      ['rain', 'nature', 'noise', 'animals', 'things', 'transport', 'urban', 'places'].indexOf(category.key)
                    ] :
                    category.key.charAt(0).toUpperCase() + category.key.slice(1)
                  }
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {category.count} {locale === 'zh' ? '个音频' : 'sounds'}
                </div>
              </Link>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}
