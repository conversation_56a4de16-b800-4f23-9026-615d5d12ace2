'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { AudioPlayer } from '@/components/AudioPlayer';
import { AudioCard } from '@/components/AudioCard';
import { MixingBoard } from '@/components/MixingBoard';
import { audioData } from '@/data/audioData';

export default function TestAudioPage() {
  const t = useTranslations('common');
  const [selectedAudio, setSelectedAudio] = useState(audioData[0]);
  const [testMode, setTestMode] = useState<'player' | 'cards' | 'mixing'>('player');

  // 只显示Rain分类的音频进行测试
  const rainAudios = audioData.filter(audio => audio.category === 'Rain').slice(0, 4);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            🎵 音频播放功能测试
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            测试音频播放器、音频卡片和混音板功能
          </p>
        </div>

        {/* 测试模式选择 */}
        <div className="flex justify-center mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm">
            <button
              onClick={() => setTestMode('player')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                testMode === 'player'
                  ? 'bg-amber-500 text-white'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              音频播放器
            </button>
            <button
              onClick={() => setTestMode('cards')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                testMode === 'cards'
                  ? 'bg-amber-500 text-white'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              音频卡片
            </button>
            <button
              onClick={() => setTestMode('mixing')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                testMode === 'mixing'
                  ? 'bg-amber-500 text-white'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              混音板
            </button>
          </div>
        </div>

        {/* 测试内容 */}
        {testMode === 'player' && (
          <div className="space-y-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                音频播放器测试
              </h2>
              
              {/* 音频选择 */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  选择测试音频：
                </label>
                <select
                  value={selectedAudio.id}
                  onChange={(e) => {
                    const audio = rainAudios.find(a => a.id === e.target.value);
                    if (audio) setSelectedAudio(audio);
                  }}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  {rainAudios.map((audio) => (
                    <option key={audio.id} value={audio.id}>
                      {audio.title.zh} ({audio.title.en})
                    </option>
                  ))}
                </select>
              </div>

              {/* 播放器变体测试 */}
              <div className="grid gap-6 md:grid-cols-3">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                    完整版播放器
                  </h3>
                  <AudioPlayer
                    sound={selectedAudio}
                    variant="full"
                    showProgress={true}
                    showVolume={true}
                    showInfo={true}
                  />
                </div>
                
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                    紧凑版播放器
                  </h3>
                  <AudioPlayer
                    sound={selectedAudio}
                    variant="compact"
                    showProgress={true}
                    showVolume={true}
                    showInfo={false}
                  />
                </div>
                
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                    迷你版播放器
                  </h3>
                  <AudioPlayer
                    sound={selectedAudio}
                    variant="mini"
                    showProgress={false}
                    showVolume={false}
                    showInfo={false}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {testMode === 'cards' && (
          <div className="space-y-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                音频卡片测试
              </h2>
              
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
                {rainAudios.map((audio) => (
                  <AudioCard
                    key={audio.id}
                    audio={audio}
                    variant="default"
                    showCategory={true}
                    showTags={true}
                    showDescription={true}
                    onPlay={(audio) => {
                      console.log('播放音频:', audio.title.zh);
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        )}

        {testMode === 'mixing' && (
          <div className="space-y-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                混音板测试
              </h2>
              
              <MixingBoard />
            </div>
          </div>
        )}

        {/* 测试说明 */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
            🧪 测试说明
          </h3>
          <div className="text-blue-800 dark:text-blue-200 space-y-2">
            <p><strong>音频播放器：</strong> 测试不同变体的播放器组件，包括播放/暂停、音量控制、进度条等功能。</p>
            <p><strong>音频卡片：</strong> 测试音频卡片的播放功能，点击播放按钮应该能够播放对应的音频文件。</p>
            <p><strong>混音板：</strong> 测试混音功能，可以添加多个音频源并同时播放（MVP版本限制2个）。</p>
            <p><strong>注意：</strong> 请确保浏览器允许自动播放音频，并检查开发者控制台是否有错误信息。</p>
          </div>
        </div>

        {/* 音频文件状态 */}
        <div className="mt-8 bg-gray-100 dark:bg-gray-800 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
            📁 测试音频文件
          </h3>
          <div className="grid gap-2 text-sm">
            {rainAudios.map((audio) => (
              <div key={audio.id} className="flex justify-between items-center py-1">
                <span className="text-gray-700 dark:text-gray-300">
                  {audio.title.zh} ({audio.filename})
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  /Sounds/{audio.category}/{audio.filename}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
