// 支持的语言类型
export type Locale = 'en' | 'zh';

// 语言方向
export type Direction = 'ltr' | 'rtl';

// 语言信息
export interface LanguageInfo {
  code: Locale;
  name: string;
  nativeName: string;
  direction: Direction;
  flag: string;
  region: string[];
}

// 翻译键的类型定义
export interface TranslationKeys {
  meta: {
    title: string;
    description: string;
    keywords: string;
  };
  
  navigation: {
    home: string;
    sounds: string;
    mix: string;
    favorites: string;
    about: string;
    settings: string;
  };
  
  common: {
    play: string;
    pause: string;
    stop: string;
    volume: string;
    timer: string;
    favorite: string;
    share: string;
    download: string;
    loading: string;
    error: string;
    retry: string;
    close: string;
    save: string;
    cancel: string;
    confirm: string;
  };
  
  audio: {
    categories: {
      rain: string;
      nature: string;
      noise: string;
      animals: string;
      things: string;
      transport: string;
      urban: string;
      places: string;
    };
    
    controls: {
      playPause: string;
      volumeControl: string;
      timerSet: string;
      loopToggle: string;
      mixingBoard: string;
    };
    
    timer: {
      '15min': string;
      '30min': string;
      '60min': string;
      '120min': string;
      custom: string;
      off: string;
    };
    
    quality: {
      sleepScore: string;
      effectiveness: string;
      safetyLevel: string;
      userRating: string;
    };
  };
  
  mixing: {
    title: string;
    addSound: string;
    removeSound: string;
    masterVolume: string;
    channelVolume: string;
    maxSounds: string;
    savePreset: string;
    loadPreset: string;
  };
  
  theme: {
    light: string;
    dark: string;
    night: string;
    auto: string;
    brightness: string;
  };
  
  language: {
    english: string;
    chinese: string;
    switchTo: string;
  };
  
  errors: {
    audioLoadFailed: string;
    networkError: string;
    browserNotSupported: string;
    audioContextFailed: string;
  };
  
  home: {
    hero: {
      title: string;
      subtitle: string;
      cta: string;
    };
    
    features: {
      scientific: {
        title: string;
        description: string;
      };
      multilingual: {
        title: string;
        description: string;
      };
      mixing: {
        title: string;
        description: string;
      };
    };
  };
}

// 本地化配置
export interface LocalizationConfig {
  locale: Locale;
  direction: Direction;
  dateFormat: string;
  timeFormat: string;
  numberFormat: {
    decimal: string;
    thousands: string;
    currency: string;
  };
  rtlSupport: boolean;
}

// 文化适应性配置
export interface CulturalAdaptation {
  locale: Locale;
  
  // 颜色偏好
  colorPreferences: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  
  // 字体配置
  typography: {
    fontFamily: string;
    fontSize: {
      base: number;
      scale: number;
    };
    lineHeight: number;
    letterSpacing: number;
  };
  
  // 布局偏好
  layout: {
    density: 'compact' | 'comfortable' | 'spacious';
    cardStyle: 'minimal' | 'elevated' | 'outlined';
    navigationStyle: 'tabs' | 'drawer' | 'bottom';
  };
  
  // 交互模式
  interaction: {
    animationDuration: number;
    hoverEffects: boolean;
    soundFeedback: boolean;
    hapticFeedback: boolean;
  };
  
  // 内容偏好
  content: {
    imageStyle: 'realistic' | 'illustrated' | 'minimal';
    iconStyle: 'outlined' | 'filled' | 'rounded';
    descriptionLength: 'short' | 'medium' | 'detailed';
  };
}

// 翻译质量指标
export interface TranslationQuality {
  locale: Locale;
  completeness: number; // 0-100%
  accuracy: number; // 0-100%
  culturalAdaptation: number; // 0-100%
  lastReviewed: string;
  reviewer: string;
  issues: {
    key: string;
    type: 'missing' | 'inaccurate' | 'cultural' | 'technical';
    description: string;
    severity: 'low' | 'medium' | 'high';
  }[];
}

// 多语言SEO配置
export interface MultilingualSEO {
  locale: Locale;
  
  // 基础SEO
  title: string;
  description: string;
  keywords: string[];
  
  // 结构化数据
  structuredData: {
    '@context': string;
    '@type': string;
    name: string;
    description: string;
    inLanguage: string;
    url: string;
  };
  
  // hreflang配置
  hreflang: {
    [key: string]: string;
  };
  
  // 本地化URL
  canonicalUrl: string;
  alternateUrls: {
    [locale: string]: string;
  };
  
  // 社交媒体
  openGraph: {
    title: string;
    description: string;
    image: string;
    locale: string;
  };
  
  twitter: {
    title: string;
    description: string;
    image: string;
  };
}

// 语言检测结果
export interface LanguageDetection {
  detectedLocale: Locale;
  confidence: number; // 0-1
  method: 'browser' | 'ip' | 'user_preference' | 'url' | 'manual';
  fallbackLocale: Locale;
  supportedLocales: Locale[];
}

// 翻译上下文
export interface TranslationContext {
  locale: Locale;
  namespace: string;
  key: string;
  defaultValue?: string;
  interpolation?: Record<string, string | number>;
  pluralization?: {
    count: number;
    forms: Record<string, string>;
  };
}

// 本地化资源
export interface LocalizationResource {
  locale: Locale;
  namespace: string;
  translations: Record<string, any>;
  metadata: {
    version: string;
    lastUpdated: string;
    translator: string;
    reviewer?: string;
    status: 'draft' | 'review' | 'approved' | 'published';
  };
}

// 导出所有国际化相关类型
export type I18nTypes = 
  | LanguageInfo
  | TranslationKeys
  | LocalizationConfig
  | CulturalAdaptation
  | TranslationQuality
  | MultilingualSEO
  | LanguageDetection
  | TranslationContext
  | LocalizationResource;
