// 音频分类类型
export type SoundCategory = 
  | 'rain' 
  | 'nature' 
  | 'noise' 
  | 'animals' 
  | 'things' 
  | 'transport' 
  | 'urban' 
  | 'places';

// 安全等级
export type SafetyLevel = 'safe' | 'caution' | 'warning';

// 噪音类型
export type NoiseType = 'white' | 'pink' | 'brown' | 'complex' | 'deep-red';

// 多语言音频元数据
export interface AudioMetadata {
  en: {
    name: string;
    description: string;
    category: string;
    tags: string[];
    scientificBasis: string;
  };
  zh: {
    name: string;
    description: string;
    category: string;
    tags: string[];
    scientificBasis: string;
  };
}

// 用户群体推荐评分
export interface UserGroupRatings {
  adults: number;          // 成人推荐得分 (0-100)
  elderly: number;         // 老年人推荐得分 (0-100)
  children: number;        // 儿童推荐得分 (0-100)
  insomnia: number;        // 失眠患者推荐得分 (0-100)
}

// 地区偏好评分
export interface RegionalPreferences {
  northAmerica: number;    // 北美地区偏好 (0-100)
  europe: number;          // 欧洲地区偏好 (0-100)
  eastAsia: number;        // 东亚地区偏好 (0-100)
  china: number;           // 中国地区偏好 (0-100)
}

// 技术参数
export interface TechnicalParameters {
  spectralSlope: number;       // 频谱斜率
  loudnessStability: number;   // 响度稳定性
  dynamicRange: number;        // 动态范围 (dB)
  tonalPeakRatio: number;      // 音调峰值比
  effectPrediction: number;    // 效果预测百分比
}

// 完整的多语言音频项目
export interface MultilingualAudioItem {
  id: string;
  filename: string;
  duration: number;            // 持续时间（秒）
  sleepScore: number;          // 睡眠评分 (0-100)
  safetyLevel: SafetyLevel;
  noiseType: NoiseType;
  category: SoundCategory;
  
  // 多语言元数据
  metadata: AudioMetadata;
  
  // 用户群体推荐
  userGroups: UserGroupRatings;
  
  // 地区偏好
  regionalPreferences: RegionalPreferences;
  
  // 技术参数
  technicalParams: TechnicalParameters;
  
  // 文件信息
  fileSize: number;            // 文件大小（字节）
  format: string;              // 文件格式 (mp3, wav, ogg)
  bitrate: number;             // 比特率
  sampleRate: number;          // 采样率
  
  // 推荐音量范围
  recommendedVolume: [number, number]; // [最小值, 最大值] (0-100)
  
  // 创建和更新时间
  createdAt: string;
  updatedAt: string;
}

// 音频播放状态
export interface AudioPlayState {
  isPlaying: boolean;
  isPaused: boolean;
  isLoading: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isLooping: boolean;
  error?: string;
}

// 音频播放器配置
export interface AudioPlayerConfig {
  autoplay: boolean;
  loop: boolean;
  volume: number;
  preload: 'none' | 'metadata' | 'auto';
  crossOrigin?: 'anonymous' | 'use-credentials';
}

// 混音通道
export interface MixingChannel {
  id: string;
  soundId: string;
  volume: number;
  isMuted: boolean;
  isActive: boolean;
  gainNode?: GainNode;
  sourceNode?: AudioBufferSourceNode;
}

// 混音器状态
export interface MixerState {
  channels: MixingChannel[];
  masterVolume: number;
  maxChannels: number;        // MVP版本限制为2
  isActive: boolean;
  audioContext?: AudioContext;
}

// 定时器配置
export interface TimerConfig {
  duration: number;           // 定时时长（分钟）
  isActive: boolean;
  remainingTime: number;      // 剩余时间（秒）
  fadeOutDuration: number;    // 渐出时长（秒）
  autoStop: boolean;
}

// 预设定时器选项
export type TimerPreset = 15 | 30 | 60 | 120; // 分钟

// 音频搜索过滤器
export interface AudioFilter {
  category?: SoundCategory;
  minSleepScore?: number;
  maxSleepScore?: number;
  safetyLevel?: SafetyLevel;
  noiseType?: NoiseType;
  userGroup?: keyof UserGroupRatings;
  searchQuery?: string;
  tags?: string[];
}

// 音频搜索结果
export interface AudioSearchResult {
  items: MultilingualAudioItem[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

// 音频推荐上下文
export interface SleepContext {
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
  userLocation?: string;
  ambientNoiseLevel?: number;
  sleepGoal: 'relaxation' | 'focus' | 'sleep' | 'meditation';
}

// 用户音频偏好
export interface UserAudioPreferences {
  favoriteCategories: SoundCategory[];
  preferredVolume: number;
  preferredDuration: number;
  dislikedSounds: string[];
  customMixes: string[];
  lastPlayed: string[];
  playHistory: {
    soundId: string;
    playedAt: string;
    duration: number;
    completionRate: number;
  }[];
}

// 音频分析结果
export interface AudioAnalysisResult {
  soundId: string;
  analysisDate: string;
  sleepEffectiveness: number;
  relaxationScore: number;
  focusScore: number;
  stressReductionScore: number;
  frequencyAnalysis: {
    lowFreq: number;    // 低频能量 (0-250Hz)
    midFreq: number;    // 中频能量 (250-4000Hz)
    highFreq: number;   // 高频能量 (4000Hz+)
  };
  recommendations: string[];
  warnings?: string[];
}

// 导出所有类型的联合类型，用于类型检查
export type AudioTypes = 
  | MultilingualAudioItem
  | AudioPlayState
  | AudioPlayerConfig
  | MixingChannel
  | MixerState
  | TimerConfig
  | AudioFilter
  | AudioSearchResult
  | SleepContext
  | UserAudioPreferences
  | AudioAnalysisResult;
