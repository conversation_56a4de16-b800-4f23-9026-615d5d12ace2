// 导出所有类型定义
export * from './audio';
export * from './user';
export * from './i18n';
export * from './global';

// 重新导出常用类型的别名
export type {
  MultilingualAudioItem as AudioItem,
  SoundCategory,
  SafetyLevel,
  NoiseType,
  AudioPlayState,
  MixerState,
  TimerConfig,
} from './audio';

export type {
  UserProfile,
  UserPreferences,
  UserBehaviorData,
  AgeGroup,
  SleepIssue,
} from './user';

export type {
  Locale,
  TranslationKeys,
  LanguageInfo,
  LocalizationConfig,
} from './i18n';

export type {
  ApiResponse,
  AppError,
  DeviceInfo,
  Theme,
  LoadingState,
  AsyncState,
} from './global';
