import { defineRouting } from 'next-intl/routing';

export const routing = defineRouting({
  // 支持的语言列表
  locales: ['en', 'zh'],
  
  // 默认语言
  defaultLocale: 'en',
  
  // 语言前缀配置
  localePrefix: {
    mode: 'as-needed', // 英文不显示前缀，中文显示/zh
    prefixes: {
      'zh': '/zh'
    }
  },

  // 路径名配置
  pathnames: {
    '/': '/',
    '/about': '/about',
    '/sounds': '/sounds',
    '/sounds/[category]': {
      en: '/sounds/[category]',
      zh: '/sounds/[category]'
    },
    '/sounds/[category]/[sound]': {
      en: '/sounds/[category]/[sound]',
      zh: '/sounds/[category]/[sound]'
    },
    '/mix': '/mix',
    '/favorites': '/favorites',
    '/settings': '/settings'
  }
});

// 导出类型
export type Pathnames = keyof typeof routing.pathnames;
export type Locale = (typeof routing.locales)[number];
