#!/usr/bin/env node

/**
 * 音频文件上传和优化脚本
 * 用于将本地音频文件上传到Cloudflare R2存储桶
 * 包含音频压缩、格式转换和CDN分发配置
 */

const fs = require('fs');
const path = require('path');
const { S3Client, PutObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3');
const ffmpeg = require('fluent-ffmpeg');
const crypto = require('crypto');

// 配置
const config = {
  r2: {
    endpoint: process.env.R2_ENDPOINT,
    accessKeyId: process.env.R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
    bucketName: process.env.R2_BUCKET_NAME || 'noisesleep-audio',
    region: 'auto'
  },
  audio: {
    inputDir: './Sounds',
    outputDir: './optimized-audio',
    formats: ['mp3', 'ogg', 'webm'],
    quality: {
      mp3: { bitrate: '128k', sampleRate: 44100 },
      ogg: { bitrate: '128k', sampleRate: 44100 },
      webm: { bitrate: '128k', sampleRate: 44100 }
    }
  }
};

// 初始化R2客户端
const r2Client = new S3Client({
  region: config.r2.region,
  endpoint: config.r2.endpoint,
  credentials: {
    accessKeyId: config.r2.accessKeyId,
    secretAccessKey: config.r2.secretAccessKey,
  },
});

/**
 * 获取文件的MD5哈希
 */
function getFileHash(filePath) {
  const fileBuffer = fs.readFileSync(filePath);
  const hashSum = crypto.createHash('md5');
  hashSum.update(fileBuffer);
  return hashSum.digest('hex');
}

/**
 * 检查文件是否已存在于R2
 */
async function fileExistsInR2(key) {
  try {
    await r2Client.send(new HeadObjectCommand({
      Bucket: config.r2.bucketName,
      Key: key
    }));
    return true;
  } catch (error) {
    if (error.name === 'NotFound') {
      return false;
    }
    throw error;
  }
}

/**
 * 音频文件压缩和格式转换
 */
function optimizeAudio(inputPath, outputPath, format) {
  return new Promise((resolve, reject) => {
    const quality = config.audio.quality[format];
    
    let command = ffmpeg(inputPath)
      .audioCodec(format === 'mp3' ? 'libmp3lame' : format === 'ogg' ? 'libvorbis' : 'libopus')
      .audioBitrate(quality.bitrate)
      .audioFrequency(quality.sampleRate)
      .audioChannels(2);

    // 音频标准化和降噪
    command = command.audioFilters([
      'loudnorm=I=-16:TP=-1.5:LRA=11', // 响度标准化
      'highpass=f=20', // 高通滤波器去除低频噪音
      'lowpass=f=20000' // 低通滤波器去除高频噪音
    ]);

    command
      .output(outputPath)
      .on('end', () => {
        console.log(`✅ 优化完成: ${outputPath}`);
        resolve();
      })
      .on('error', (err) => {
        console.error(`❌ 优化失败: ${inputPath}`, err);
        reject(err);
      })
      .on('progress', (progress) => {
        if (progress.percent) {
          process.stdout.write(`\r⏳ 处理中: ${Math.round(progress.percent)}%`);
        }
      })
      .run();
  });
}

/**
 * 上传文件到R2
 */
async function uploadToR2(filePath, key, contentType) {
  try {
    const fileStream = fs.createReadStream(filePath);
    const stats = fs.statSync(filePath);
    
    const uploadParams = {
      Bucket: config.r2.bucketName,
      Key: key,
      Body: fileStream,
      ContentType: contentType,
      ContentLength: stats.size,
      CacheControl: 'public, max-age=2592000', // 30天缓存
      Metadata: {
        'upload-date': new Date().toISOString(),
        'file-hash': getFileHash(filePath),
        'optimized': 'true'
      }
    };

    await r2Client.send(new PutObjectCommand(uploadParams));
    console.log(`✅ 上传成功: ${key}`);
    return true;
  } catch (error) {
    console.error(`❌ 上传失败: ${key}`, error);
    return false;
  }
}

/**
 * 获取内容类型
 */
function getContentType(extension) {
  const types = {
    '.mp3': 'audio/mpeg',
    '.ogg': 'audio/ogg',
    '.webm': 'audio/webm',
    '.wav': 'audio/wav'
  };
  return types[extension] || 'application/octet-stream';
}

/**
 * 处理单个音频文件
 */
async function processAudioFile(inputPath, category) {
  const fileName = path.basename(inputPath, path.extname(inputPath));
  const inputExt = path.extname(inputPath);
  
  console.log(`\n🎵 处理音频文件: ${fileName}`);
  
  // 确保输出目录存在
  const categoryOutputDir = path.join(config.audio.outputDir, category);
  if (!fs.existsSync(categoryOutputDir)) {
    fs.mkdirSync(categoryOutputDir, { recursive: true });
  }
  
  const results = [];
  
  // 处理每种格式
  for (const format of config.audio.formats) {
    const outputFileName = `${fileName}.${format}`;
    const outputPath = path.join(categoryOutputDir, outputFileName);
    const r2Key = `sounds/${category}/${outputFileName}`;
    
    try {
      // 检查是否已存在
      if (await fileExistsInR2(r2Key)) {
        console.log(`⏭️  跳过已存在文件: ${r2Key}`);
        continue;
      }
      
      // 如果是原始格式且质量符合要求，直接上传
      if (inputExt.slice(1) === format && format === 'mp3') {
        const success = await uploadToR2(inputPath, r2Key, getContentType(`.${format}`));
        results.push({ format, success, path: r2Key });
      } else {
        // 需要转换格式
        await optimizeAudio(inputPath, outputPath, format);
        const success = await uploadToR2(outputPath, r2Key, getContentType(`.${format}`));
        results.push({ format, success, path: r2Key });
        
        // 清理临时文件
        if (fs.existsSync(outputPath)) {
          fs.unlinkSync(outputPath);
        }
      }
    } catch (error) {
      console.error(`❌ 处理格式 ${format} 失败:`, error);
      results.push({ format, success: false, error: error.message });
    }
  }
  
  return results;
}

/**
 * 扫描并处理所有音频文件
 */
async function processAllAudioFiles() {
  console.log('🚀 开始音频文件上传和优化流程...\n');
  
  const categories = fs.readdirSync(config.audio.inputDir, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name);
  
  const summary = {
    totalFiles: 0,
    successfulUploads: 0,
    failedUploads: 0,
    categories: {}
  };
  
  for (const category of categories) {
    console.log(`\n📁 处理分类: ${category}`);
    const categoryPath = path.join(config.audio.inputDir, category);
    const audioFiles = fs.readdirSync(categoryPath)
      .filter(file => /\.(mp3|wav|ogg|webm)$/i.test(file))
      .map(file => path.join(categoryPath, file));
    
    summary.categories[category] = {
      files: audioFiles.length,
      successful: 0,
      failed: 0
    };
    
    for (const audioFile of audioFiles) {
      summary.totalFiles++;
      const results = await processAudioFile(audioFile, category);
      
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;
      
      summary.successfulUploads += successful;
      summary.failedUploads += failed;
      summary.categories[category].successful += successful;
      summary.categories[category].failed += failed;
    }
  }
  
  // 打印总结
  console.log('\n📊 上传总结:');
  console.log(`总文件数: ${summary.totalFiles}`);
  console.log(`成功上传: ${summary.successfulUploads}`);
  console.log(`失败上传: ${summary.failedUploads}`);
  console.log('\n分类详情:');
  
  Object.entries(summary.categories).forEach(([category, stats]) => {
    console.log(`  ${category}: ${stats.successful}/${stats.files * config.audio.formats.length} 成功`);
  });
  
  console.log('\n✨ 音频文件处理完成!');
}

// 主函数
async function main() {
  try {
    // 检查必要的环境变量
    if (!config.r2.accessKeyId || !config.r2.secretAccessKey || !config.r2.endpoint) {
      throw new Error('缺少必要的R2配置环境变量');
    }
    
    // 检查输入目录
    if (!fs.existsSync(config.audio.inputDir)) {
      throw new Error(`音频输入目录不存在: ${config.audio.inputDir}`);
    }
    
    // 创建输出目录
    if (!fs.existsSync(config.audio.outputDir)) {
      fs.mkdirSync(config.audio.outputDir, { recursive: true });
    }
    
    await processAllAudioFiles();
  } catch (error) {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  processAudioFile,
  processAllAudioFiles,
  uploadToR2,
  optimizeAudio
};
