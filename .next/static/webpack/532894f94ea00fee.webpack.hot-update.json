{"c": ["app/[locale]/landing/page", "app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/howler/dist/howler.js", "(app-pages-browser)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "(app-pages-browser)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "(app-pages-browser)/./node_modules/use-sync-external-store/shim/index.js", "(app-pages-browser)/./node_modules/use-sync-external-store/shim/with-selector.js", "(app-pages-browser)/./node_modules/zustand/esm/index.mjs", "(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs", "(app-pages-browser)/./node_modules/zustand/esm/vanilla.mjs", "(app-pages-browser)/./src/data/audioData.ts", "(app-pages-browser)/./src/hooks/useAudioPlayer.ts", "(app-pages-browser)/./src/store/audioStore.ts"]}