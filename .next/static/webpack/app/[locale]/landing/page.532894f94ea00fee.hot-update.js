"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/landing/page",{

/***/ "(app-pages-browser)/./src/components/CategoryCard/CategoryCard.tsx":
/*!******************************************************!*\
  !*** ./src/components/CategoryCard/CategoryCard.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CategoryCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CategoryCard(param) {\n    let { icon, type, name, description, className = \"\" } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"landing\");\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getTypeColor = (type)=>{\n        const colors = {\n            noise: \"bg-blue-100 text-blue-800\",\n            things: \"bg-gray-100 text-gray-800\",\n            transport: \"bg-yellow-100 text-yellow-800\",\n            places: \"bg-purple-100 text-purple-800\",\n            urban: \"bg-orange-100 text-orange-800\",\n            animals: \"bg-green-100 text-green-800\",\n            rain: \"bg-cyan-100 text-cyan-800\",\n            nature: \"bg-emerald-100 text-emerald-800\"\n        };\n        return colors[type] || \"bg-gray-100 text-gray-800\";\n    };\n    const handlePlay = ()=>{\n        // 简单的演示功能，切换播放状态\n        setIsPlaying(!isPlaying);\n        // 3秒后自动停止（模拟播放）\n        if (!isPlaying) {\n            setTimeout(()=>{\n                setIsPlaying(false);\n            }, 3000);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all border border-gray-100 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-4xl\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getTypeColor(type)),\n                        children: t(\"typeLabels.\".concat(type))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"text-xl font-medium text-gray-800 mb-2\",\n                children: name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: description\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handlePlay,\n                className: \"w-full py-3 rounded-full transition-all \".concat(isPlaying ? \"bg-indigo-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-indigo-100\"),\n                children: isPlaying ? t(\"buttons.pause\") : t(\"buttons.play\")\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(CategoryCard, \"6BKLHYxqTPDHxuVOHwepAh0BWQs=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations\n    ];\n});\n_c = CategoryCard;\nvar _c;\n$RefreshReg$(_c, \"CategoryCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CategoryCard/CategoryCard.tsx\n"));

/***/ })

});