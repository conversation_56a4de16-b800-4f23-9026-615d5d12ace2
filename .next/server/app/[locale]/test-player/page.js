/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/test-player/page";
exports.ids = ["app/[locale]/test-player/page"];
exports.modules = {

/***/ "(rsc)/./src/i18n/locales lazy recursive ^\\.\\/.*\\.json$":
/*!****************************************************************!*\
  !*** ./src/i18n/locales/ lazy ^\.\/.*\.json$ namespace object ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./src/i18n/locales/en.json",
		"_rsc_src_i18n_locales_en_json"
	],
	"./zh.json": [
		"(rsc)/./src/i18n/locales/zh.json",
		"_rsc_src_i18n_locales_zh_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./src/i18n/locales lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Ftest-player%2Fpage&page=%2F%5Blocale%5D%2Ftest-player%2Fpage&appPaths=%2F%5Blocale%5D%2Ftest-player%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Ftest-player%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Ftest-player%2Fpage&page=%2F%5Blocale%5D%2Ftest-player%2Fpage&appPaths=%2F%5Blocale%5D%2Ftest-player%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Ftest-player%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'test-player',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/test-player/page.tsx */ \"(rsc)/./src/app/[locale]/test-player/page.tsx\")), \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\")), \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/test-player/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/test-player/page\",\n        pathname: \"/[locale]/test-player\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkYlNUJsb2NhbGUlNUQlMkZ0ZXN0LXBsYXllciUyRnBhZ2UmcGFnZT0lMkYlNUJsb2NhbGUlNUQlMkZ0ZXN0LXBsYXllciUyRnBhZ2UmYXBwUGF0aHM9JTJGJTVCbG9jYWxlJTVEJTJGdGVzdC1wbGF5ZXIlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGJTVCbG9jYWxlJTVEJTJGdGVzdC1wbGF5ZXIlMkZwYWdlLnRzeCZhcHBEaXI9JTJGVXNlcnMlMkZvX28lMkZEb2N1bWVudHMlMkZOb2lzZVNsZWVwJTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZVc2VycyUyRm9fbyUyRkRvY3VtZW50cyUyRk5vaXNlU2xlZXAmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLDBMQUEwRztBQUNqSTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLHlCQUF5QixzS0FBZ0c7QUFDekg7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUF1RjtBQUNoSCxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm9pc2VzbGVlcC13ZWIvPzRiOGYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnW2xvY2FsZV0nLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICd0ZXN0LXBsYXllcicsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9vX28vRG9jdW1lbnRzL05vaXNlU2xlZXAvc3JjL2FwcC9bbG9jYWxlXS90ZXN0LXBsYXllci9wYWdlLnRzeFwiKSwgXCIvVXNlcnMvb19vL0RvY3VtZW50cy9Ob2lzZVNsZWVwL3NyYy9hcHAvW2xvY2FsZV0vdGVzdC1wbGF5ZXIvcGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9vX28vRG9jdW1lbnRzL05vaXNlU2xlZXAvc3JjL2FwcC9bbG9jYWxlXS9sYXlvdXQudHN4XCIpLCBcIi9Vc2Vycy9vX28vRG9jdW1lbnRzL05vaXNlU2xlZXAvc3JjL2FwcC9bbG9jYWxlXS9sYXlvdXQudHN4XCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9vX28vRG9jdW1lbnRzL05vaXNlU2xlZXAvc3JjL2FwcC9sYXlvdXQudHN4XCIpLCBcIi9Vc2Vycy9vX28vRG9jdW1lbnRzL05vaXNlU2xlZXAvc3JjL2FwcC9sYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCIvVXNlcnMvb19vL0RvY3VtZW50cy9Ob2lzZVNsZWVwL3NyYy9hcHAvW2xvY2FsZV0vdGVzdC1wbGF5ZXIvcGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9bbG9jYWxlXS90ZXN0LXBsYXllci9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL1tsb2NhbGVdL3Rlc3QtcGxheWVyL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1tsb2NhbGVdL3Rlc3QtcGxheWVyXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Ftest-player%2Fpage&page=%2F%5Blocale%5D%2Ftest-player%2Fpage&appPaths=%2F%5Blocale%5D%2Ftest-player%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Ftest-player%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayer.tsx%22%2C%22ids%22%3A%5B%22AudioPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayerProvider.tsx%22%2C%22ids%22%3A%5B%22AudioPlayerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FPlayButton.tsx%22%2C%22ids%22%3A%5B%22PlayButton%22%2C%22PrimaryPlayButton%22%2C%22SecondaryPlayButton%22%2C%22GhostPlayButton%22%2C%22LargePlayButton%22%2C%22SmallPlayButton%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FProgressBar.tsx%22%2C%22ids%22%3A%5B%22ProgressBar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FStandardPlayer.tsx%22%2C%22ids%22%3A%5B%22StandardPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FVolumeControl.tsx%22%2C%22ids%22%3A%5B%22VolumeControl%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayer.tsx%22%2C%22ids%22%3A%5B%22AudioPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayerProvider.tsx%22%2C%22ids%22%3A%5B%22AudioPlayerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FPlayButton.tsx%22%2C%22ids%22%3A%5B%22PlayButton%22%2C%22PrimaryPlayButton%22%2C%22SecondaryPlayButton%22%2C%22GhostPlayButton%22%2C%22LargePlayButton%22%2C%22SmallPlayButton%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FProgressBar.tsx%22%2C%22ids%22%3A%5B%22ProgressBar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FStandardPlayer.tsx%22%2C%22ids%22%3A%5B%22StandardPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FVolumeControl.tsx%22%2C%22ids%22%3A%5B%22VolumeControl%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/AudioPlayer.tsx */ \"(ssr)/./src/components/AudioPlayer/AudioPlayer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/AudioPlayerProvider.tsx */ \"(ssr)/./src/components/AudioPlayer/AudioPlayerProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/PlayButton.tsx */ \"(ssr)/./src/components/AudioPlayer/PlayButton.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/ProgressBar.tsx */ \"(ssr)/./src/components/AudioPlayer/ProgressBar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/StandardPlayer.tsx */ \"(ssr)/./src/components/AudioPlayer/StandardPlayer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/VolumeControl.tsx */ \"(ssr)/./src/components/AudioPlayer/VolumeControl.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayer.tsx%22%2C%22ids%22%3A%5B%22AudioPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayerProvider.tsx%22%2C%22ids%22%3A%5B%22AudioPlayerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FPlayButton.tsx%22%2C%22ids%22%3A%5B%22PlayButton%22%2C%22PrimaryPlayButton%22%2C%22SecondaryPlayButton%22%2C%22GhostPlayButton%22%2C%22LargePlayButton%22%2C%22SmallPlayButton%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FProgressBar.tsx%22%2C%22ids%22%3A%5B%22ProgressBar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FStandardPlayer.tsx%22%2C%22ids%22%3A%5B%22StandardPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FVolumeControl.tsx%22%2C%22ids%22%3A%5B%22VolumeControl%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2F%5Blocale%5D%2Ftest-player%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2F%5Blocale%5D%2Ftest-player%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/test-player/page.tsx */ \"(ssr)/./src/app/[locale]/test-player/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGb19vJTJGRG9jdW1lbnRzJTJGTm9pc2VTbGVlcCUyRnNyYyUyRmFwcCUyRiU1QmxvY2FsZSU1RCUyRnRlc3QtcGxheWVyJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBMQUEwRyIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLz82Y2M0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL29fby9Eb2N1bWVudHMvTm9pc2VTbGVlcC9zcmMvYXBwL1tsb2NhbGVdL3Rlc3QtcGxheWVyL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2F%5Blocale%5D%2Ftest-player%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/test-player/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/[locale]/test-player/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestPlayerPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_AudioCard_AudioCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/AudioCard/AudioCard */ \"(ssr)/./src/components/AudioCard/AudioCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// 测试音频数据\nconst testAudio = {\n    id: \"test-rain-1\",\n    title: {\n        zh: \"轻柔雨声\",\n        en: \"Gentle Rain\"\n    },\n    description: {\n        zh: \"舒缓的雨声，帮助放松和睡眠\",\n        en: \"Soothing rain sounds for relaxation and sleep\"\n    },\n    category: \"rain\",\n    tags: [\n        \"rain\",\n        \"nature\",\n        \"sleep\"\n    ],\n    duration: 180,\n    fileUrl: \"/audio/rain/gentle-rain.mp3\",\n    imageUrl: \"/images/rain/gentle-rain.jpg\",\n    rating: 4.8,\n    playCount: 15420,\n    createdAt: \"2024-01-01T00:00:00Z\",\n    updatedAt: \"2024-01-01T00:00:00Z\"\n};\nfunction TestPlayerPage() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"common\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4\",\n                            children: \"音频播放器测试页面\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"点击下面的音频卡片来测试播放器功能\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioCard_AudioCard__WEBPACK_IMPORTED_MODULE_1__.AudioCard, {\n                            audio: testAudio,\n                            variant: \"card\",\n                            showDuration: true,\n                            showRating: true,\n                            showPlayCount: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioCard_AudioCard__WEBPACK_IMPORTED_MODULE_1__.AudioCard, {\n                            audio: {\n                                ...testAudio,\n                                id: \"test-rain-2\",\n                                title: {\n                                    zh: \"暴雨声\",\n                                    en: \"Heavy Rain\"\n                                },\n                                description: {\n                                    zh: \"强烈的暴雨声，适合深度专注\",\n                                    en: \"Intense rain sounds for deep focus\"\n                                }\n                            },\n                            variant: \"card\",\n                            showDuration: true,\n                            showRating: true,\n                            showPlayCount: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioCard_AudioCard__WEBPACK_IMPORTED_MODULE_1__.AudioCard, {\n                            audio: {\n                                ...testAudio,\n                                id: \"test-rain-3\",\n                                title: {\n                                    zh: \"雨滴声\",\n                                    en: \"Rain Drops\"\n                                },\n                                description: {\n                                    zh: \"清脆的雨滴声，营造宁静氛围\",\n                                    en: \"Crisp rain drops creating peaceful atmosphere\"\n                                }\n                            },\n                            variant: \"list\",\n                            showDuration: true,\n                            showRating: false,\n                            showPlayCount: false\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4\",\n                            children: \"测试说明\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-2 text-gray-600 dark:text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 点击任意音频卡片应该会显示底部播放器\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 播放器应该显示当前播放的音频信息\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 播放器应该有播放/暂停、停止、音量控制等功能\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 在桌面端应该显示定时器和混音按钮\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 在移动端这些按钮应该被隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 播放器应该支持最小化和关闭\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 播放器应该有平滑的动画效果\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/test-player/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioCard/AudioCard.tsx":
/*!************************************************!*\
  !*** ./src/components/AudioCard/AudioCard.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioCard: () => (/* binding */ AudioCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/audioStore */ \"(ssr)/./src/store/audioStore.ts\");\n/* harmony import */ var _hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAudioPlayer */ \"(ssr)/./src/hooks/useAudioPlayer.ts\");\n/* harmony import */ var _components_AudioPlayer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AudioPlayer */ \"(ssr)/./src/components/AudioPlayer/index.ts\");\n/* __next_internal_client_entry_do_not_use__ AudioCard auto */ \n\n\n\n\n\n\nfunction AudioCard({ audio, variant = \"default\", showCategory = true, showTags = true, showDuration = false, showDescription = false, onPlay, className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"common\");\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useLocale)();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { currentSound, playState, favorites, addToFavorites, removeFromFavorites } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_3__.useAudioStore)();\n    const { play, pause, isPlaying, isLoading } = (0,_hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__.useAudioPlayer)();\n    const isCurrentlyPlaying = currentSound?.id === audio.id && isPlaying;\n    const isFavorite = favorites.includes(audio.id);\n    // 获取本地化文本\n    const getLocalizedText = (textObj)=>{\n        return textObj[locale] || textObj.en || Object.values(textObj)[0] || \"\";\n    };\n    const title = getLocalizedText(audio.title);\n    const description = audio.description ? getLocalizedText(audio.description) : \"\";\n    // 处理播放\n    const handlePlay = ()=>{\n        if (isCurrentlyPlaying) {\n            pause();\n        } else {\n            play(audio);\n        }\n        onPlay?.(audio);\n    };\n    // 处理收藏\n    const handleToggleFavorite = (e)=>{\n        e.stopPropagation();\n        if (isFavorite) {\n            removeFromFavorites(audio.id);\n        } else {\n            addToFavorites(audio.id);\n        }\n    };\n    // 获取音频图标\n    const getAudioIcon = ()=>{\n        const iconMap = {\n            rain: \"\\uD83C\\uDF27️\",\n            nature: \"\\uD83C\\uDF3F\",\n            noise: \"\\uD83D\\uDD0A\",\n            animals: \"\\uD83D\\uDC3E\",\n            things: \"\\uD83C\\uDFE0\",\n            transport: \"\\uD83D\\uDE97\",\n            urban: \"\\uD83C\\uDFD9️\",\n            places: \"\\uD83D\\uDCCD\"\n        };\n        return iconMap[audio.category.toLowerCase()] || \"\\uD83C\\uDFB5\";\n    };\n    // 格式化时长\n    const formatDuration = (seconds)=>{\n        if (!seconds || seconds <= 0) return \"\";\n        const mins = Math.floor(seconds / 60);\n        const secs = Math.floor(seconds % 60);\n        return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n    };\n    // 紧凑版本\n    if (variant === \"compact\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg\", \"border border-gray-200 dark:border-gray-700 hover:border-amber-300 dark:hover:border-amber-600\", \"transition-all duration-200 cursor-pointer group\", isCurrentlyPlaying && \"ring-2 ring-amber-500 border-amber-500\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioPlayer__WEBPACK_IMPORTED_MODULE_5__.SmallPlayButton, {\n                    isPlaying: isCurrentlyPlaying,\n                    isLoading: isLoading && currentSound?.id === audio.id,\n                    onPlay: handlePlay,\n                    onPause: handlePlay\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        showCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                            children: [\n                                getAudioIcon(),\n                                \" \",\n                                audio.category\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggleFavorite,\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1 rounded-full transition-colors opacity-0 group-hover:opacity-100\", isFavorite && \"opacity-100\", \"hover:bg-gray-100 dark:hover:bg-gray-700\", isFavorite ? \"text-red-500\" : \"text-gray-400\"),\n                    \"aria-label\": isFavorite ? t(\"removeFromFavorites\") : t(\"addToFavorites\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: isFavorite ? \"currentColor\" : \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this);\n    }\n    // 详细版本\n    if (variant === \"detailed\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700\", \"hover:shadow-md hover:border-amber-300 dark:hover:border-amber-600\", \"transition-all duration-200 overflow-hidden group\", isCurrentlyPlaying && \"ring-2 ring-amber-500 border-amber-500\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-32 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-4xl\",\n                                children: getAudioIcon()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioPlayer__WEBPACK_IMPORTED_MODULE_5__.SmallPlayButton, {\n                                isPlaying: isCurrentlyPlaying,\n                                isLoading: isLoading && currentSound?.id === audio.id,\n                                onPlay: handlePlay,\n                                onPause: handlePlay,\n                                variant: \"primary\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleToggleFavorite,\n                            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"absolute top-2 right-2 p-2 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\", \"transition-all duration-200 opacity-0 group-hover:opacity-100\", isFavorite && \"opacity-100\", \"hover:bg-white dark:hover:bg-gray-800\", isFavorite ? \"text-red-500\" : \"text-gray-400\"),\n                            \"aria-label\": isFavorite ? t(\"removeFromFavorites\") : t(\"addToFavorites\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: isFavorite ? \"currentColor\" : \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        showDescription && description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 mb-3\",\n                            children: [\n                                showCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 rounded-full\",\n                                    children: [\n                                        getAudioIcon(),\n                                        \" \",\n                                        audio.category\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this),\n                                showTags && audio.tags?.slice(0, 2).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-full\",\n                                        children: tag\n                                    }, tag, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\",\n                            children: [\n                                showDuration && audio.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: formatDuration(audio.duration)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this),\n                                audio.scientificRating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"⭐\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: audio.scientificRating.toFixed(1)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this);\n    }\n    // 默认版本\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\", \"hover:shadow-md hover:border-amber-300 dark:hover:border-amber-600\", \"transition-all duration-200 overflow-hidden cursor-pointer group\", isCurrentlyPlaying && \"ring-2 ring-amber-500 border-amber-500\", className),\n        onClick: handlePlay,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: getAudioIcon()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                            children: title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        showCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: audio.category\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        isCurrentlyPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-3 bg-amber-500 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-4 bg-amber-500 rounded-full animate-pulse\",\n                                    style: {\n                                        animationDelay: \"0.2s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-3 bg-amber-500 rounded-full animate-pulse\",\n                                    style: {\n                                        animationDelay: \"0.4s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this),\n                showDescription && description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this),\n                showTags && audio.tags && audio.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-1 mb-3\",\n                    children: [\n                        audio.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-full\",\n                                children: tag\n                            }, tag, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, this)),\n                        audio.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"px-2 py-1 text-xs font-medium text-gray-500 dark:text-gray-500\",\n                            children: [\n                                \"+\",\n                                audio.tags.length - 3\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioPlayer__WEBPACK_IMPORTED_MODULE_5__.SmallPlayButton, {\n                                    isPlaying: isCurrentlyPlaying,\n                                    isLoading: isLoading && currentSound?.id === audio.id,\n                                    onPlay: (e)=>{\n                                        e?.stopPropagation();\n                                        handlePlay();\n                                    },\n                                    onPause: (e)=>{\n                                        e?.stopPropagation();\n                                        handlePlay();\n                                    },\n                                    variant: \"ghost\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                showDuration && audio.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                    children: formatDuration(audio.duration)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                audio.scientificRating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"⭐\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: audio.scientificRating.toFixed(1)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleToggleFavorite,\n                                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-700\", isFavorite ? \"text-red-500\" : \"text-gray-400\"),\n                                    \"aria-label\": isFavorite ? t(\"removeFromFavorites\") : t(\"addToFavorites\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: isFavorite ? \"currentColor\" : \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioCard/AudioCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/AudioPlayer.tsx":
/*!****************************************************!*\
  !*** ./src/components/AudioPlayer/AudioPlayer.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioPlayer: () => (/* binding */ AudioPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAudioPlayer */ \"(ssr)/./src/hooks/useAudioPlayer.ts\");\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/audioStore */ \"(ssr)/./src/store/audioStore.ts\");\n/* harmony import */ var _PlayButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PlayButton */ \"(ssr)/./src/components/AudioPlayer/PlayButton.tsx\");\n/* harmony import */ var _VolumeControl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./VolumeControl */ \"(ssr)/./src/components/AudioPlayer/VolumeControl.tsx\");\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ProgressBar */ \"(ssr)/./src/components/AudioPlayer/ProgressBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ AudioPlayer auto */ \n\n\n\n\n\n\n\nfunction AudioPlayer({ sound, variant = \"full\", showProgress = true, showVolume = true, showInfo = true, className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"common\");\n    const { currentSound, favorites, addToFavorites, removeFromFavorites } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_3__.useAudioStore)();\n    const { play, pause, stop, setVolume, setLoop, seek, isPlaying, isPaused, isLoading, currentTime, duration, volume, isLooping, error } = (0,_hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_2__.useAudioPlayer)();\n    // 使用传入的 sound 或当前播放的 sound\n    const displaySound = sound || currentSound;\n    const isFavorite = displaySound ? favorites.includes(displaySound.id) : false;\n    // 播放控制\n    const handlePlay = ()=>{\n        if (sound && sound.id !== currentSound?.id) {\n            play(sound);\n        } else {\n            play();\n        }\n    };\n    const handlePause = ()=>{\n        pause();\n    };\n    const handleStop = ()=>{\n        stop();\n    };\n    // 收藏切换\n    const toggleFavorite = ()=>{\n        if (!displaySound) return;\n        if (isFavorite) {\n            removeFromFavorites(displaySound.id);\n        } else {\n            addToFavorites(displaySound.id);\n        }\n    };\n    // 循环切换\n    const toggleLoop = ()=>{\n        setLoop(!isLooping);\n    };\n    // 获取音频标题\n    const getAudioTitle = (audioItem, locale = \"en\")=>{\n        return audioItem.title[locale] || audioItem.title.en;\n    };\n    // 获取音频描述\n    const getAudioDescription = (audioItem, locale = \"en\")=>{\n        return audioItem.description?.[locale] || audioItem.description?.en || \"\";\n    };\n    if (!displaySound) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"flex items-center justify-center p-8 text-gray-500 dark:text-gray-400\", \"bg-gray-50 dark:bg-gray-900 rounded-lg border-2 border-dashed border-gray-200 dark:border-gray-700\", className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: t(\"noAudioSelected\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    }\n    // 紧凑版本\n    if (variant === \"compact\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PlayButton__WEBPACK_IMPORTED_MODULE_4__.PlayButton, {\n                    isPlaying: isPlaying && currentSound?.id === displaySound.id,\n                    isLoading: isLoading,\n                    onPlay: handlePlay,\n                    onPause: handlePause,\n                    size: \"sm\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this),\n                showInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\",\n                            children: getAudioTitle(displaySound)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                            children: displaySound.category\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 11\n                }, this),\n                showVolume && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VolumeControl__WEBPACK_IMPORTED_MODULE_5__.VolumeControl, {\n                        volume: volume,\n                        onVolumeChange: setVolume,\n                        size: \"sm\",\n                        showIcon: false\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, this);\n    }\n    // 迷你版本\n    if (variant === \"mini\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"flex items-center gap-2\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PlayButton__WEBPACK_IMPORTED_MODULE_4__.PlayButton, {\n                    isPlaying: isPlaying && currentSound?.id === displaySound.id,\n                    isLoading: isLoading,\n                    onPlay: handlePlay,\n                    onPause: handlePause,\n                    size: \"sm\",\n                    variant: \"ghost\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this),\n                showInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-600 dark:text-gray-400 truncate\",\n                    children: getAudioTitle(displaySound)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this);\n    }\n    // 完整版本\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden\", className),\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-red-600 dark:text-red-400\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this),\n            showInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1\",\n                                    children: getAudioTitle(displaySound)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                    children: getAudioDescription(displaySound)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-xs text-gray-500 dark:text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full\",\n                                            children: displaySound.category\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        displaySound.tags?.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleFavorite,\n                            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-700\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", isFavorite ? \"text-red-500\" : \"text-gray-400\"),\n                            \"aria-label\": isFavorite ? t(\"removeFromFavorites\") : t(\"addToFavorites\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: isFavorite ? \"currentColor\" : \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this),\n            showProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressBar__WEBPACK_IMPORTED_MODULE_6__.ProgressBar, {\n                    currentTime: currentTime,\n                    duration: duration,\n                    onSeek: seek,\n                    isLoading: isLoading,\n                    showTime: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleLoop,\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-700\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", isLooping ? \"text-amber-500\" : \"text-gray-400\"),\n                                \"aria-label\": isLooping ? t(\"disableLoop\") : t(\"enableLoop\"),\n                                title: isLooping ? t(\"disableLoop\") : t(\"enableLoop\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M7 7h10v3l4-4-4-4v3H5v6h2V7zm10 10H7v-3l-4 4 4 4v-3h12v-6h-2v4z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleStop,\n                                    disabled: !isPlaying && !isPaused,\n                                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-700\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"disabled:opacity-50 disabled:cursor-not-allowed\", \"text-gray-600 dark:text-gray-400\"),\n                                    \"aria-label\": t(\"stop\"),\n                                    title: t(\"stop\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M6 6h12v12H6z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PlayButton__WEBPACK_IMPORTED_MODULE_4__.PlayButton, {\n                                    isPlaying: isPlaying && currentSound?.id === displaySound.id,\n                                    isLoading: isLoading,\n                                    onPlay: handlePlay,\n                                    onPause: handlePause,\n                                    size: \"lg\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this),\n                        showVolume && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VolumeControl__WEBPACK_IMPORTED_MODULE_5__.VolumeControl, {\n                                volume: volume,\n                                onVolumeChange: setVolume,\n                                size: \"md\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BdWRpb1BsYXllci9BdWRpb1BsYXllci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ2hCO0FBQzRCO0FBQ0w7QUFFVDtBQUNNO0FBQ0o7QUFXckMsU0FBU08sWUFBWSxFQUMxQkMsS0FBSyxFQUNMQyxVQUFVLE1BQU0sRUFDaEJDLGVBQWUsSUFBSSxFQUNuQkMsYUFBYSxJQUFJLEVBQ2pCQyxXQUFXLElBQUksRUFDZkMsU0FBUyxFQUNRO0lBQ2pCLE1BQU1DLElBQUlkLDBEQUFlQSxDQUFDO0lBQzFCLE1BQU0sRUFBRWUsWUFBWSxFQUFFQyxTQUFTLEVBQUVDLGNBQWMsRUFBRUMsbUJBQW1CLEVBQUUsR0FBR2YsZ0VBQWFBO0lBRXRGLE1BQU0sRUFDSmdCLElBQUksRUFDSkMsS0FBSyxFQUNMQyxJQUFJLEVBQ0pDLFNBQVMsRUFDVEMsT0FBTyxFQUNQQyxJQUFJLEVBQ0pDLFNBQVMsRUFDVEMsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLFdBQVcsRUFDWEMsUUFBUSxFQUNSQyxNQUFNLEVBQ05DLFNBQVMsRUFDVEMsS0FBSyxFQUNOLEdBQUc5QixxRUFBY0E7SUFFbEIsMkJBQTJCO0lBQzNCLE1BQU0rQixlQUFlekIsU0FBU087SUFDOUIsTUFBTW1CLGFBQWFELGVBQWVqQixVQUFVbUIsUUFBUSxDQUFDRixhQUFhRyxFQUFFLElBQUk7SUFFeEUsT0FBTztJQUNQLE1BQU1DLGFBQWE7UUFDakIsSUFBSTdCLFNBQVNBLE1BQU00QixFQUFFLEtBQUtyQixjQUFjcUIsSUFBSTtZQUMxQ2pCLEtBQUtYO1FBQ1AsT0FBTztZQUNMVztRQUNGO0lBQ0Y7SUFFQSxNQUFNbUIsY0FBYztRQUNsQmxCO0lBQ0Y7SUFFQSxNQUFNbUIsYUFBYTtRQUNqQmxCO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTW1CLGlCQUFpQjtRQUNyQixJQUFJLENBQUNQLGNBQWM7UUFFbkIsSUFBSUMsWUFBWTtZQUNkaEIsb0JBQW9CZSxhQUFhRyxFQUFFO1FBQ3JDLE9BQU87WUFDTG5CLGVBQWVnQixhQUFhRyxFQUFFO1FBQ2hDO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTUssYUFBYTtRQUNqQmxCLFFBQVEsQ0FBQ1E7SUFDWDtJQUVBLFNBQVM7SUFDVCxNQUFNVyxnQkFBZ0IsQ0FBQ0MsV0FBa0NDLFNBQWlCLElBQUk7UUFDNUUsT0FBT0QsVUFBVUUsS0FBSyxDQUFDRCxPQUF1QyxJQUFJRCxVQUFVRSxLQUFLLENBQUNDLEVBQUU7SUFDdEY7SUFFQSxTQUFTO0lBQ1QsTUFBTUMsc0JBQXNCLENBQUNKLFdBQWtDQyxTQUFpQixJQUFJO1FBQ2xGLE9BQU9ELFVBQVVLLFdBQVcsRUFBRSxDQUFDSixPQUE2QyxJQUFJRCxVQUFVSyxXQUFXLEVBQUVGLE1BQU07SUFDL0c7SUFFQSxJQUFJLENBQUNiLGNBQWM7UUFDakIscUJBQ0UsOERBQUNnQjtZQUFJcEMsV0FBV1osMENBQUlBLENBQ2xCLHlFQUNBLHNHQUNBWTtzQkFFQSw0RUFBQ29DO2dCQUFJcEMsV0FBVTs7a0NBQ2IsOERBQUNvQzt3QkFBSXBDLFdBQVU7a0NBQ2IsNEVBQUNxQzs0QkFBSUMsTUFBSzs0QkFBZUMsU0FBUTtzQ0FDL0IsNEVBQUNDO2dDQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O2tDQUdaLDhEQUFDQzt3QkFBRTFDLFdBQVU7a0NBQVdDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSWxDO0lBRUEsT0FBTztJQUNQLElBQUlMLFlBQVksV0FBVztRQUN6QixxQkFDRSw4REFBQ3dDO1lBQUlwQyxXQUFXWiwwQ0FBSUEsQ0FDbEIsMEhBQ0FZOzs4QkFFQSw4REFBQ1QsbURBQVVBO29CQUNUcUIsV0FBV0EsYUFBYVYsY0FBY3FCLE9BQU9ILGFBQWFHLEVBQUU7b0JBQzVEVCxXQUFXQTtvQkFDWDZCLFFBQVFuQjtvQkFDUm9CLFNBQVNuQjtvQkFDVG9CLE1BQUs7Ozs7OztnQkFHTjlDLDBCQUNDLDhEQUFDcUM7b0JBQUlwQyxXQUFVOztzQ0FDYiw4REFBQzhDOzRCQUFHOUMsV0FBVTtzQ0FDWDZCLGNBQWNUOzs7Ozs7c0NBRWpCLDhEQUFDc0I7NEJBQUUxQyxXQUFVO3NDQUNWb0IsYUFBYTJCLFFBQVE7Ozs7Ozs7Ozs7OztnQkFLM0JqRCw0QkFDQyw4REFBQ3NDO29CQUFJcEMsV0FBVTs4QkFDYiw0RUFBQ1IseURBQWFBO3dCQUNaeUIsUUFBUUE7d0JBQ1IrQixnQkFBZ0J2Qzt3QkFDaEJvQyxNQUFLO3dCQUNMSSxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztJQU10QjtJQUVBLE9BQU87SUFDUCxJQUFJckQsWUFBWSxRQUFRO1FBQ3RCLHFCQUNFLDhEQUFDd0M7WUFBSXBDLFdBQVdaLDBDQUFJQSxDQUFDLDJCQUEyQlk7OzhCQUM5Qyw4REFBQ1QsbURBQVVBO29CQUNUcUIsV0FBV0EsYUFBYVYsY0FBY3FCLE9BQU9ILGFBQWFHLEVBQUU7b0JBQzVEVCxXQUFXQTtvQkFDWDZCLFFBQVFuQjtvQkFDUm9CLFNBQVNuQjtvQkFDVG9CLE1BQUs7b0JBQ0xqRCxTQUFROzs7Ozs7Z0JBRVRHLDBCQUNDLDhEQUFDbUQ7b0JBQUtsRCxXQUFVOzhCQUNiNkIsY0FBY1Q7Ozs7Ozs7Ozs7OztJQUt6QjtJQUVBLE9BQU87SUFDUCxxQkFDRSw4REFBQ2dCO1FBQUlwQyxXQUFXWiwwQ0FBSUEsQ0FDbEIsOEdBQ0FZOztZQUdDbUIsdUJBQ0MsOERBQUNpQjtnQkFBSXBDLFdBQVU7MEJBQ2IsNEVBQUMwQztvQkFBRTFDLFdBQVU7OEJBQTBDbUI7Ozs7Ozs7Ozs7O1lBSzFEcEIsMEJBQ0MsOERBQUNxQztnQkFBSXBDLFdBQVU7MEJBQ2IsNEVBQUNvQztvQkFBSXBDLFdBQVU7O3NDQUNiLDhEQUFDb0M7NEJBQUlwQyxXQUFVOzs4Q0FDYiw4REFBQ21EO29DQUFHbkQsV0FBVTs4Q0FDWDZCLGNBQWNUOzs7Ozs7OENBRWpCLDhEQUFDc0I7b0NBQUUxQyxXQUFVOzhDQUNWa0Msb0JBQW9CZDs7Ozs7OzhDQUV2Qiw4REFBQ2dCO29DQUFJcEMsV0FBVTs7c0RBQ2IsOERBQUNrRDs0Q0FBS2xELFdBQVU7c0RBQ2JvQixhQUFhMkIsUUFBUTs7Ozs7O3dDQUV2QjNCLGFBQWFnQyxJQUFJLEVBQUVDLElBQUksQ0FBQ0Msb0JBQ3ZCLDhEQUFDSjtnREFBZWxELFdBQVU7MERBQ3ZCc0Q7K0NBRFFBOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FRakIsOERBQUNDOzRCQUNDQyxTQUFTN0I7NEJBQ1QzQixXQUFXWiwwQ0FBSUEsQ0FDYixzQ0FDQSw0Q0FDQSx3REFDQWlDLGFBQWEsaUJBQWlCOzRCQUVoQ29DLGNBQVlwQyxhQUFhcEIsRUFBRSx5QkFBeUJBLEVBQUU7c0NBRXRELDRFQUFDb0M7Z0NBQUlyQyxXQUFVO2dDQUFVc0MsTUFBTWpCLGFBQWEsaUJBQWlCO2dDQUFRcUMsUUFBTztnQ0FBZW5CLFNBQVE7MENBQ2pHLDRFQUFDQztvQ0FBS21CLGVBQWM7b0NBQVFDLGdCQUFlO29DQUFRQyxhQUFhO29DQUFHcEIsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUTlFNUMsOEJBQ0MsOERBQUN1QztnQkFBSXBDLFdBQVU7MEJBQ2IsNEVBQUNQLHFEQUFXQTtvQkFDVnNCLGFBQWFBO29CQUNiQyxVQUFVQTtvQkFDVjhDLFFBQVFuRDtvQkFDUkcsV0FBV0E7b0JBQ1hpRCxVQUFVOzs7Ozs7Ozs7OzswQkFNaEIsOERBQUMzQjtnQkFBSXBDLFdBQVU7MEJBQ2IsNEVBQUNvQztvQkFBSXBDLFdBQVU7O3NDQUViLDhEQUFDb0M7NEJBQUlwQyxXQUFVO3NDQUNiLDRFQUFDdUQ7Z0NBQ0NDLFNBQVM1QjtnQ0FDVDVCLFdBQVdaLDBDQUFJQSxDQUNiLHNDQUNBLDRDQUNBLHdEQUNBOEIsWUFBWSxtQkFBbUI7Z0NBRWpDdUMsY0FBWXZDLFlBQVlqQixFQUFFLGlCQUFpQkEsRUFBRTtnQ0FDN0MrQixPQUFPZCxZQUFZakIsRUFBRSxpQkFBaUJBLEVBQUU7MENBRXhDLDRFQUFDb0M7b0NBQUlyQyxXQUFVO29DQUFVc0MsTUFBSztvQ0FBZUMsU0FBUTs4Q0FDbkQsNEVBQUNDO3dDQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTWQsOERBQUNMOzRCQUFJcEMsV0FBVTs7OENBQ2IsOERBQUN1RDtvQ0FDQ0MsU0FBUzlCO29DQUNUc0MsVUFBVSxDQUFDcEQsYUFBYSxDQUFDQztvQ0FDekJiLFdBQVdaLDBDQUFJQSxDQUNiLHNDQUNBLDRDQUNBLHdEQUNBLG1EQUNBO29DQUVGcUUsY0FBWXhELEVBQUU7b0NBQ2QrQixPQUFPL0IsRUFBRTs4Q0FFVCw0RUFBQ29DO3dDQUFJckMsV0FBVTt3Q0FBVXNDLE1BQUs7d0NBQWVDLFNBQVE7a0RBQ25ELDRFQUFDQzs0Q0FBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJWiw4REFBQ2xELG1EQUFVQTtvQ0FDVHFCLFdBQVdBLGFBQWFWLGNBQWNxQixPQUFPSCxhQUFhRyxFQUFFO29DQUM1RFQsV0FBV0E7b0NBQ1g2QixRQUFRbkI7b0NBQ1JvQixTQUFTbkI7b0NBQ1RvQixNQUFLOzs7Ozs7Ozs7Ozs7d0JBS1IvQyw0QkFDQyw4REFBQ3NDOzRCQUFJcEMsV0FBVTtzQ0FDYiw0RUFBQ1IseURBQWFBO2dDQUNaeUIsUUFBUUE7Z0NBQ1IrQixnQkFBZ0J2QztnQ0FDaEJvQyxNQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm9pc2VzbGVlcC13ZWIvLi9zcmMvY29tcG9uZW50cy9BdWRpb1BsYXllci9BdWRpb1BsYXllci50c3g/NzYyYiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9ucyB9IGZyb20gJ25leHQtaW50bCc7XG5pbXBvcnQgeyBjbHN4IH0gZnJvbSAnY2xzeCc7XG5pbXBvcnQgeyB1c2VBdWRpb1BsYXllciB9IGZyb20gJ0AvaG9va3MvdXNlQXVkaW9QbGF5ZXInO1xuaW1wb3J0IHsgdXNlQXVkaW9TdG9yZSB9IGZyb20gJ0Avc3RvcmUvYXVkaW9TdG9yZSc7XG5pbXBvcnQgeyBNdWx0aWxpbmd1YWxBdWRpb0l0ZW0gfSBmcm9tICdAL3R5cGVzL2F1ZGlvJztcbmltcG9ydCB7IFBsYXlCdXR0b24gfSBmcm9tICcuL1BsYXlCdXR0b24nO1xuaW1wb3J0IHsgVm9sdW1lQ29udHJvbCB9IGZyb20gJy4vVm9sdW1lQ29udHJvbCc7XG5pbXBvcnQgeyBQcm9ncmVzc0JhciB9IGZyb20gJy4vUHJvZ3Jlc3NCYXInO1xuXG5pbnRlcmZhY2UgQXVkaW9QbGF5ZXJQcm9wcyB7XG4gIHNvdW5kPzogTXVsdGlsaW5ndWFsQXVkaW9JdGVtO1xuICB2YXJpYW50PzogJ2NvbXBhY3QnIHwgJ2Z1bGwnIHwgJ21pbmknO1xuICBzaG93UHJvZ3Jlc3M/OiBib29sZWFuO1xuICBzaG93Vm9sdW1lPzogYm9vbGVhbjtcbiAgc2hvd0luZm8/OiBib29sZWFuO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBBdWRpb1BsYXllcih7XG4gIHNvdW5kLFxuICB2YXJpYW50ID0gJ2Z1bGwnLFxuICBzaG93UHJvZ3Jlc3MgPSB0cnVlLFxuICBzaG93Vm9sdW1lID0gdHJ1ZSxcbiAgc2hvd0luZm8gPSB0cnVlLFxuICBjbGFzc05hbWUsXG59OiBBdWRpb1BsYXllclByb3BzKSB7XG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoJ2NvbW1vbicpO1xuICBjb25zdCB7IGN1cnJlbnRTb3VuZCwgZmF2b3JpdGVzLCBhZGRUb0Zhdm9yaXRlcywgcmVtb3ZlRnJvbUZhdm9yaXRlcyB9ID0gdXNlQXVkaW9TdG9yZSgpO1xuICBcbiAgY29uc3Qge1xuICAgIHBsYXksXG4gICAgcGF1c2UsXG4gICAgc3RvcCxcbiAgICBzZXRWb2x1bWUsXG4gICAgc2V0TG9vcCxcbiAgICBzZWVrLFxuICAgIGlzUGxheWluZyxcbiAgICBpc1BhdXNlZCxcbiAgICBpc0xvYWRpbmcsXG4gICAgY3VycmVudFRpbWUsXG4gICAgZHVyYXRpb24sXG4gICAgdm9sdW1lLFxuICAgIGlzTG9vcGluZyxcbiAgICBlcnJvcixcbiAgfSA9IHVzZUF1ZGlvUGxheWVyKCk7XG5cbiAgLy8g5L2/55So5Lyg5YWl55qEIHNvdW5kIOaIluW9k+WJjeaSreaUvueahCBzb3VuZFxuICBjb25zdCBkaXNwbGF5U291bmQgPSBzb3VuZCB8fCBjdXJyZW50U291bmQ7XG4gIGNvbnN0IGlzRmF2b3JpdGUgPSBkaXNwbGF5U291bmQgPyBmYXZvcml0ZXMuaW5jbHVkZXMoZGlzcGxheVNvdW5kLmlkKSA6IGZhbHNlO1xuXG4gIC8vIOaSreaUvuaOp+WItlxuICBjb25zdCBoYW5kbGVQbGF5ID0gKCkgPT4ge1xuICAgIGlmIChzb3VuZCAmJiBzb3VuZC5pZCAhPT0gY3VycmVudFNvdW5kPy5pZCkge1xuICAgICAgcGxheShzb3VuZCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHBsYXkoKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUGF1c2UgPSAoKSA9PiB7XG4gICAgcGF1c2UoKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdG9wID0gKCkgPT4ge1xuICAgIHN0b3AoKTtcbiAgfTtcblxuICAvLyDmlLbol4/liIfmjaJcbiAgY29uc3QgdG9nZ2xlRmF2b3JpdGUgPSAoKSA9PiB7XG4gICAgaWYgKCFkaXNwbGF5U291bmQpIHJldHVybjtcbiAgICBcbiAgICBpZiAoaXNGYXZvcml0ZSkge1xuICAgICAgcmVtb3ZlRnJvbUZhdm9yaXRlcyhkaXNwbGF5U291bmQuaWQpO1xuICAgIH0gZWxzZSB7XG4gICAgICBhZGRUb0Zhdm9yaXRlcyhkaXNwbGF5U291bmQuaWQpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlvqrnjq/liIfmjaJcbiAgY29uc3QgdG9nZ2xlTG9vcCA9ICgpID0+IHtcbiAgICBzZXRMb29wKCFpc0xvb3BpbmcpO1xuICB9O1xuXG4gIC8vIOiOt+WPlumfs+mikeagh+mimFxuICBjb25zdCBnZXRBdWRpb1RpdGxlID0gKGF1ZGlvSXRlbTogTXVsdGlsaW5ndWFsQXVkaW9JdGVtLCBsb2NhbGU6IHN0cmluZyA9ICdlbicpID0+IHtcbiAgICByZXR1cm4gYXVkaW9JdGVtLnRpdGxlW2xvY2FsZSBhcyBrZXlvZiB0eXBlb2YgYXVkaW9JdGVtLnRpdGxlXSB8fCBhdWRpb0l0ZW0udGl0bGUuZW47XG4gIH07XG5cbiAgLy8g6I635Y+W6Z+z6aKR5o+P6L+wXG4gIGNvbnN0IGdldEF1ZGlvRGVzY3JpcHRpb24gPSAoYXVkaW9JdGVtOiBNdWx0aWxpbmd1YWxBdWRpb0l0ZW0sIGxvY2FsZTogc3RyaW5nID0gJ2VuJykgPT4ge1xuICAgIHJldHVybiBhdWRpb0l0ZW0uZGVzY3JpcHRpb24/Lltsb2NhbGUgYXMga2V5b2YgdHlwZW9mIGF1ZGlvSXRlbS5kZXNjcmlwdGlvbl0gfHwgYXVkaW9JdGVtLmRlc2NyaXB0aW9uPy5lbiB8fCAnJztcbiAgfTtcblxuICBpZiAoIWRpc3BsYXlTb3VuZCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzeChcbiAgICAgICAgJ2ZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtOCB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMCcsXG4gICAgICAgICdiZy1ncmF5LTUwIGRhcms6YmctZ3JheS05MDAgcm91bmRlZC1sZyBib3JkZXItMiBib3JkZXItZGFzaGVkIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCcsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTMwMCBkYXJrOnRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgIDxzdmcgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggZD1cIk0xMiAzdjEwLjU1Yy0uNTktLjM0LTEuMjctLjU1LTItLjU1LTIuMjEgMC00IDEuNzktNCA0czEuNzkgNCA0IDQgNC0xLjc5IDQtNFY3aDRWM2gtNnpcIi8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e3QoJ25vQXVkaW9TZWxlY3RlZCcpfTwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8g57Sn5YeR54mI5pysXG4gIGlmICh2YXJpYW50ID09PSAnY29tcGFjdCcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9e2Nsc3goXG4gICAgICAgICdmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBwLTMgYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNzAwJyxcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfT5cbiAgICAgICAgPFBsYXlCdXR0b25cbiAgICAgICAgICBpc1BsYXlpbmc9e2lzUGxheWluZyAmJiBjdXJyZW50U291bmQ/LmlkID09PSBkaXNwbGF5U291bmQuaWR9XG4gICAgICAgICAgaXNMb2FkaW5nPXtpc0xvYWRpbmd9XG4gICAgICAgICAgb25QbGF5PXtoYW5kbGVQbGF5fVxuICAgICAgICAgIG9uUGF1c2U9e2hhbmRsZVBhdXNlfVxuICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgIC8+XG4gICAgICAgIFxuICAgICAgICB7c2hvd0luZm8gJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgIHtnZXRBdWRpb1RpdGxlKGRpc3BsYXlTb3VuZCl9XG4gICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICB7ZGlzcGxheVNvdW5kLmNhdGVnb3J5fVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHtzaG93Vm9sdW1lICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjBcIj5cbiAgICAgICAgICAgIDxWb2x1bWVDb250cm9sXG4gICAgICAgICAgICAgIHZvbHVtZT17dm9sdW1lfVxuICAgICAgICAgICAgICBvblZvbHVtZUNoYW5nZT17c2V0Vm9sdW1lfVxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICBzaG93SWNvbj17ZmFsc2V9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIOi/t+S9oOeJiOacrFxuICBpZiAodmFyaWFudCA9PT0gJ21pbmknKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbHN4KCdmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMicsIGNsYXNzTmFtZSl9PlxuICAgICAgICA8UGxheUJ1dHRvblxuICAgICAgICAgIGlzUGxheWluZz17aXNQbGF5aW5nICYmIGN1cnJlbnRTb3VuZD8uaWQgPT09IGRpc3BsYXlTb3VuZC5pZH1cbiAgICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cbiAgICAgICAgICBvblBsYXk9e2hhbmRsZVBsYXl9XG4gICAgICAgICAgb25QYXVzZT17aGFuZGxlUGF1c2V9XG4gICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAvPlxuICAgICAgICB7c2hvd0luZm8gJiYgKFxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgIHtnZXRBdWRpb1RpdGxlKGRpc3BsYXlTb3VuZCl9XG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIOWujOaVtOeJiOacrFxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbHN4KFxuICAgICAgJ2JnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcm91bmRlZC14bCBzaGFkb3ctbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCBvdmVyZmxvdy1oaWRkZW4nLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX0+XG4gICAgICB7Lyog6ZSZ6K+v5o+Q56S6ICovfVxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYmctcmVkLTUwIGRhcms6YmctcmVkLTkwMC8yMCBib3JkZXItYiBib3JkZXItcmVkLTIwMCBkYXJrOmJvcmRlci1yZWQtODAwXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDAgZGFyazp0ZXh0LXJlZC00MDBcIj57ZXJyb3J9PC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDpn7PpopHkv6Hmga8gKi99XG4gICAgICB7c2hvd0luZm8gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBwYi00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICB7Z2V0QXVkaW9UaXRsZShkaXNwbGF5U291bmQpfVxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICB7Z2V0QXVkaW9EZXNjcmlwdGlvbihkaXNwbGF5U291bmQpfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ncmF5LTEwMCBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAge2Rpc3BsYXlTb3VuZC5jYXRlZ29yeX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAge2Rpc3BsYXlTb3VuZC50YWdzPy5tYXAoKHRhZykgPT4gKFxuICAgICAgICAgICAgICAgICAgPHNwYW4ga2V5PXt0YWd9IGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ncmF5LTEwMCBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICB7dGFnfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7Lyog5pS26JeP5oyJ6ZKuICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVGYXZvcml0ZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbHN4KFxuICAgICAgICAgICAgICAgICdwLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tY29sb3JzJyxcbiAgICAgICAgICAgICAgICAnaG92ZXI6YmctZ3JheS0xMDAgZGFyazpob3ZlcjpiZy1ncmF5LTcwMCcsXG4gICAgICAgICAgICAgICAgJ2ZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hbWJlci01MDAnLFxuICAgICAgICAgICAgICAgIGlzRmF2b3JpdGUgPyAndGV4dC1yZWQtNTAwJyA6ICd0ZXh0LWdyYXktNDAwJ1xuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICBhcmlhLWxhYmVsPXtpc0Zhdm9yaXRlID8gdCgncmVtb3ZlRnJvbUZhdm9yaXRlcycpIDogdCgnYWRkVG9GYXZvcml0ZXMnKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD17aXNGYXZvcml0ZSA/ICdjdXJyZW50Q29sb3InIDogJ25vbmUnfSBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTQuMzE4IDYuMzE4YTQuNSA0LjUgMCAwMDAgNi4zNjRMMTIgMjAuMzY0bDcuNjgyLTcuNjgyYTQuNSA0LjUgMCAwMC02LjM2NC02LjM2NEwxMiA3LjYzNmwtMS4zMTgtMS4zMThhNC41IDQuNSAwIDAwLTYuMzY0IDB6XCIgLz5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog6L+b5bqm5p2hICovfVxuICAgICAge3Nob3dQcm9ncmVzcyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBwYi00XCI+XG4gICAgICAgICAgPFByb2dyZXNzQmFyXG4gICAgICAgICAgICBjdXJyZW50VGltZT17Y3VycmVudFRpbWV9XG4gICAgICAgICAgICBkdXJhdGlvbj17ZHVyYXRpb259XG4gICAgICAgICAgICBvblNlZWs9e3NlZWt9XG4gICAgICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cbiAgICAgICAgICAgIHNob3dUaW1lPXt0cnVlfVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIOaOp+WItumdouadvyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBwYi02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgey8qIOW3puS+p+aOp+WItiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZUxvb3B9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17Y2xzeChcbiAgICAgICAgICAgICAgICAncC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycycsXG4gICAgICAgICAgICAgICAgJ2hvdmVyOmJnLWdyYXktMTAwIGRhcms6aG92ZXI6YmctZ3JheS03MDAnLFxuICAgICAgICAgICAgICAgICdmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYW1iZXItNTAwJyxcbiAgICAgICAgICAgICAgICBpc0xvb3BpbmcgPyAndGV4dC1hbWJlci01MDAnIDogJ3RleHQtZ3JheS00MDAnXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9e2lzTG9vcGluZyA/IHQoJ2Rpc2FibGVMb29wJykgOiB0KCdlbmFibGVMb29wJyl9XG4gICAgICAgICAgICAgIHRpdGxlPXtpc0xvb3BpbmcgPyB0KCdkaXNhYmxlTG9vcCcpIDogdCgnZW5hYmxlTG9vcCcpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTVcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNNyA3aDEwdjNsNC00LTQtNHYzSDV2NmgyVjd6bTEwIDEwSDd2LTNsLTQgNCA0IDR2LTNoMTJ2LTZoLTJ2NHpcIi8+XG4gICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5Lit5aSu5pKt5pS+5o6n5Yi2ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU3RvcH1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFpc1BsYXlpbmcgJiYgIWlzUGF1c2VkfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Nsc3goXG4gICAgICAgICAgICAgICAgJ3AtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnMnLFxuICAgICAgICAgICAgICAgICdob3ZlcjpiZy1ncmF5LTEwMCBkYXJrOmhvdmVyOmJnLWdyYXktNzAwJyxcbiAgICAgICAgICAgICAgICAnZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWFtYmVyLTUwMCcsXG4gICAgICAgICAgICAgICAgJ2Rpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkJyxcbiAgICAgICAgICAgICAgICAndGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAnXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9e3QoJ3N0b3AnKX1cbiAgICAgICAgICAgICAgdGl0bGU9e3QoJ3N0b3AnKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTYgNmgxMnYxMkg2elwiLz5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgPFBsYXlCdXR0b25cbiAgICAgICAgICAgICAgaXNQbGF5aW5nPXtpc1BsYXlpbmcgJiYgY3VycmVudFNvdW5kPy5pZCA9PT0gZGlzcGxheVNvdW5kLmlkfVxuICAgICAgICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cbiAgICAgICAgICAgICAgb25QbGF5PXtoYW5kbGVQbGF5fVxuICAgICAgICAgICAgICBvblBhdXNlPXtoYW5kbGVQYXVzZX1cbiAgICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5Y+z5L6n6Z+z6YeP5o6n5Yi2ICovfVxuICAgICAgICAgIHtzaG93Vm9sdW1lICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zMlwiPlxuICAgICAgICAgICAgICA8Vm9sdW1lQ29udHJvbFxuICAgICAgICAgICAgICAgIHZvbHVtZT17dm9sdW1lfVxuICAgICAgICAgICAgICAgIG9uVm9sdW1lQ2hhbmdlPXtzZXRWb2x1bWV9XG4gICAgICAgICAgICAgICAgc2l6ZT1cIm1kXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlVHJhbnNsYXRpb25zIiwiY2xzeCIsInVzZUF1ZGlvUGxheWVyIiwidXNlQXVkaW9TdG9yZSIsIlBsYXlCdXR0b24iLCJWb2x1bWVDb250cm9sIiwiUHJvZ3Jlc3NCYXIiLCJBdWRpb1BsYXllciIsInNvdW5kIiwidmFyaWFudCIsInNob3dQcm9ncmVzcyIsInNob3dWb2x1bWUiLCJzaG93SW5mbyIsImNsYXNzTmFtZSIsInQiLCJjdXJyZW50U291bmQiLCJmYXZvcml0ZXMiLCJhZGRUb0Zhdm9yaXRlcyIsInJlbW92ZUZyb21GYXZvcml0ZXMiLCJwbGF5IiwicGF1c2UiLCJzdG9wIiwic2V0Vm9sdW1lIiwic2V0TG9vcCIsInNlZWsiLCJpc1BsYXlpbmciLCJpc1BhdXNlZCIsImlzTG9hZGluZyIsImN1cnJlbnRUaW1lIiwiZHVyYXRpb24iLCJ2b2x1bWUiLCJpc0xvb3BpbmciLCJlcnJvciIsImRpc3BsYXlTb3VuZCIsImlzRmF2b3JpdGUiLCJpbmNsdWRlcyIsImlkIiwiaGFuZGxlUGxheSIsImhhbmRsZVBhdXNlIiwiaGFuZGxlU3RvcCIsInRvZ2dsZUZhdm9yaXRlIiwidG9nZ2xlTG9vcCIsImdldEF1ZGlvVGl0bGUiLCJhdWRpb0l0ZW0iLCJsb2NhbGUiLCJ0aXRsZSIsImVuIiwiZ2V0QXVkaW9EZXNjcmlwdGlvbiIsImRlc2NyaXB0aW9uIiwiZGl2Iiwic3ZnIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZCIsInAiLCJvblBsYXkiLCJvblBhdXNlIiwic2l6ZSIsImg0IiwiY2F0ZWdvcnkiLCJvblZvbHVtZUNoYW5nZSIsInNob3dJY29uIiwic3BhbiIsImgzIiwidGFncyIsIm1hcCIsInRhZyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJhcmlhLWxhYmVsIiwic3Ryb2tlIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJvblNlZWsiLCJzaG93VGltZSIsImRpc2FibGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/AudioPlayer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/AudioPlayerProvider.tsx":
/*!************************************************************!*\
  !*** ./src/components/AudioPlayer/AudioPlayerProvider.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioPlayerProvider: () => (/* binding */ AudioPlayerProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/audioStore */ \"(ssr)/./src/store/audioStore.ts\");\n/* harmony import */ var _StandardPlayer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StandardPlayer */ \"(ssr)/./src/components/AudioPlayer/StandardPlayer.tsx\");\n/* __next_internal_client_entry_do_not_use__ AudioPlayerProvider auto */ \n\n\n\nfunction AudioPlayerProvider({ children, className }) {\n    const { currentSound, playerUI, setPlayerVisible } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_2__.useAudioStore)();\n    // 监听音频播放状态，自动显示/隐藏播放器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentSound && !playerUI.isVisible) {\n            setPlayerVisible(true);\n        }\n    }, [\n        currentSound,\n        playerUI.isVisible,\n        setPlayerVisible\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            children,\n            playerUI.mode === \"standard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StandardPlayer__WEBPACK_IMPORTED_MODULE_3__.StandardPlayer, {\n                position: playerUI.position,\n                showMixingButton: true,\n                showSleepModeButton: true,\n                autoHide: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this),\n            playerUI.mode === \"sleep\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-gray-900 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-white text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"睡眠模式\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 mb-8\",\n                            children: \"睡眠模式界面将在第三阶段实现\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>_store_audioStore__WEBPACK_IMPORTED_MODULE_2__.useAudioStore.getState().setPlayerMode(\"standard\"),\n                            className: \"px-6 py-3 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors\",\n                            children: \"返回标准模式\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/AudioPlayerProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/PlayButton.tsx":
/*!***************************************************!*\
  !*** ./src/components/AudioPlayer/PlayButton.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GhostPlayButton: () => (/* binding */ GhostPlayButton),\n/* harmony export */   LargePlayButton: () => (/* binding */ LargePlayButton),\n/* harmony export */   PlayButton: () => (/* binding */ PlayButton),\n/* harmony export */   PrimaryPlayButton: () => (/* binding */ PrimaryPlayButton),\n/* harmony export */   SecondaryPlayButton: () => (/* binding */ SecondaryPlayButton),\n/* harmony export */   SmallPlayButton: () => (/* binding */ SmallPlayButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ PlayButton,PrimaryPlayButton,SecondaryPlayButton,GhostPlayButton,LargePlayButton,SmallPlayButton auto */ \n\n\nfunction PlayButton({ isPlaying, isLoading, onPlay, onPause, size = \"md\", variant = \"primary\", disabled = false, className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"common\");\n    const handleClick = ()=>{\n        if (disabled || isLoading) return;\n        if (isPlaying) {\n            onPause();\n        } else {\n            onPlay();\n        }\n    };\n    const sizeClasses = {\n        sm: \"w-8 h-8 p-1.5\",\n        md: \"w-12 h-12 p-3\",\n        lg: \"w-16 h-16 p-4\"\n    };\n    const variantClasses = {\n        primary: \"bg-amber-500 hover:bg-amber-600 text-white shadow-lg hover:shadow-xl\",\n        secondary: \"bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200\",\n        ghost: \"bg-transparent hover:bg-gray-100 text-gray-600 dark:hover:bg-gray-800 dark:text-gray-400\"\n    };\n    const iconSize = {\n        sm: \"w-3 h-3\",\n        md: \"w-4 h-4\",\n        lg: \"w-6 h-6\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleClick,\n        disabled: disabled || isLoading,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(// 基础样式\n        \"relative rounded-full transition-all duration-200 ease-in-out\", \"focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2\", \"disabled:opacity-50 disabled:cursor-not-allowed\", \"transform hover:scale-105 active:scale-95\", // 尺寸样式\n        sizeClasses[size], // 变体样式\n        variantClasses[variant], // 自定义类名\n        className),\n        \"aria-label\": isPlaying ? t(\"pause\") : t(\"play\"),\n        title: isPlaying ? t(\"pause\") : t(\"play\"),\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"animate-spin rounded-full border-2 border-current border-t-transparent\", iconSize[size])\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this),\n            !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: isPlaying ? // 暂停图标\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: iconSize[size],\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M6 4h4v16H6V4zm8 0h4v16h-4V4z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 13\n                }, this) : // 播放图标\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(iconSize[size], \"ml-0.5\"),\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8 5v14l11-7z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this),\n            isPlaying && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-full animate-ping bg-current opacity-20\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n// 预设的播放按钮变体\nfunction PrimaryPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        variant: \"primary\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 127,\n        columnNumber: 10\n    }, this);\n}\nfunction SecondaryPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        variant: \"secondary\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 131,\n        columnNumber: 10\n    }, this);\n}\nfunction GhostPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        variant: \"ghost\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 135,\n        columnNumber: 10\n    }, this);\n}\n// 大型播放按钮（用于主播放器）\nfunction LargePlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        size: \"lg\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 140,\n        columnNumber: 10\n    }, this);\n}\n// 小型播放按钮（用于列表项）\nfunction SmallPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        size: \"sm\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 145,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/PlayButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/ProgressBar.tsx":
/*!****************************************************!*\
  !*** ./src/components/AudioPlayer/ProgressBar.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgressBar: () => (/* binding */ ProgressBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ ProgressBar auto */ \n\n\nfunction ProgressBar({ currentTime, duration, onSeek, isLoading = false, showTime = true, size = \"md\", className, disabled = false }) {\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragTime, setDragTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tooltipTime, setTooltipTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const progressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 格式化时间显示\n    const formatTime = (seconds)=>{\n        if (!isFinite(seconds) || seconds < 0) return \"0:00\";\n        const mins = Math.floor(seconds / 60);\n        const secs = Math.floor(seconds % 60);\n        return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n    };\n    // 计算进度百分比\n    const progress = duration > 0 ? currentTime / duration * 100 : 0;\n    const displayTime = isDragging ? dragTime : currentTime;\n    const displayProgress = isDragging ? dragTime / duration * 100 : progress;\n    // 处理拖拽开始\n    const handlePointerDown = (event)=>{\n        if (disabled || duration === 0) return;\n        setIsDragging(true);\n        updateTimeFromEvent(event);\n        event.preventDefault();\n    };\n    // 处理鼠标移动（用于显示预览时间）\n    const handleMouseMove = (event)=>{\n        if (!progressRef.current || duration === 0) return;\n        const rect = progressRef.current.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const percentage = Math.max(0, Math.min(1, x / rect.width));\n        const time = percentage * duration;\n        setTooltipTime(time);\n        setShowTooltip(true);\n    };\n    // 处理鼠标离开\n    const handleMouseLeave = ()=>{\n        if (!isDragging) {\n            setShowTooltip(false);\n        }\n    };\n    // 从事件更新时间\n    const updateTimeFromEvent = (event)=>{\n        if (!progressRef.current || duration === 0) return;\n        const rect = progressRef.current.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const percentage = Math.max(0, Math.min(1, x / rect.width));\n        const time = percentage * duration;\n        setDragTime(time);\n        setTooltipTime(time);\n    };\n    // 处理拖拽移动\n    const handlePointerMove = (event)=>{\n        if (!isDragging || disabled) return;\n        updateTimeFromEvent(event);\n    };\n    // 处理拖拽结束\n    const handlePointerUp = ()=>{\n        if (isDragging) {\n            onSeek(dragTime);\n            setIsDragging(false);\n            setShowTooltip(false);\n        }\n    };\n    // 处理点击跳转\n    const handleClick = (event)=>{\n        if (disabled || duration === 0 || isDragging) return;\n        const rect = progressRef.current?.getBoundingClientRect();\n        if (!rect) return;\n        const x = event.clientX - rect.left;\n        const percentage = Math.max(0, Math.min(1, x / rect.width));\n        const time = percentage * duration;\n        onSeek(time);\n    };\n    // 键盘控制\n    const handleKeyDown = (event)=>{\n        if (disabled || duration === 0) return;\n        let newTime = currentTime;\n        const step = duration * 0.05; // 5% 步长\n        switch(event.key){\n            case \"ArrowLeft\":\n                newTime = Math.max(0, currentTime - step);\n                break;\n            case \"ArrowRight\":\n                newTime = Math.min(duration, currentTime + step);\n                break;\n            case \"Home\":\n                newTime = 0;\n                break;\n            case \"End\":\n                newTime = duration;\n                break;\n            default:\n                return;\n        }\n        event.preventDefault();\n        onSeek(newTime);\n    };\n    // 监听全局鼠标事件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"pointermove\", handlePointerMove);\n            document.addEventListener(\"pointerup\", handlePointerUp);\n            return ()=>{\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                document.removeEventListener(\"pointerup\", handlePointerUp);\n            };\n        }\n    }, [\n        isDragging,\n        dragTime\n    ]);\n    const sizeClasses = {\n        sm: \"h-1\",\n        md: \"h-2\",\n        lg: \"h-3\"\n    };\n    const thumbSize = {\n        sm: \"w-3 h-3\",\n        md: \"w-4 h-4\",\n        lg: \"w-5 h-5\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"w-full\", className),\n        children: [\n            showTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-2 text-sm text-gray-600 dark:text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-mono\",\n                        children: formatTime(displayTime)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-mono\",\n                        children: formatTime(duration)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: progressRef,\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"relative bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer group\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", sizeClasses[size], disabled && \"opacity-50 cursor-not-allowed\"),\n                        onPointerDown: handlePointerDown,\n                        onClick: handleClick,\n                        onMouseMove: handleMouseMove,\n                        onMouseLeave: handleMouseLeave,\n                        onKeyDown: handleKeyDown,\n                        tabIndex: disabled ? -1 : 0,\n                        role: \"slider\",\n                        \"aria-valuemin\": 0,\n                        \"aria-valuemax\": duration,\n                        \"aria-valuenow\": currentTime,\n                        \"aria-label\": \"音频进度\",\n                        children: [\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gray-300 dark:bg-gray-600 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"absolute left-0 top-0 bg-amber-500 rounded-full transition-all duration-150\", sizeClasses[size]),\n                                style: {\n                                    width: `${Math.max(0, Math.min(100, displayProgress))}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            duration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"absolute top-1/2 bg-white border-2 border-amber-500 rounded-full shadow-md\", \"transform -translate-x-1/2 -translate-y-1/2 transition-all duration-150\", \"opacity-0 group-hover:opacity-100\", isDragging && \"opacity-100 scale-125\", thumbSize[size]),\n                                style: {\n                                    left: `${Math.max(0, Math.min(100, displayProgress))}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    showTooltip && duration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute z-10 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg transform -translate-x-1/2 -top-8\",\n                        style: {\n                            left: `${Math.max(0, Math.min(100, tooltipTime / duration * 100))}%`\n                        },\n                        children: formatTime(tooltipTime)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            duration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-1 text-xs text-gray-500 dark:text-gray-500 text-center\",\n                children: [\n                    Math.round(displayProgress),\n                    \"%\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BdWRpb1BsYXllci9Qcm9ncmVzc0Jhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVvRDtBQUN4QjtBQWFyQixTQUFTSSxZQUFZLEVBQzFCQyxXQUFXLEVBQ1hDLFFBQVEsRUFDUkMsTUFBTSxFQUNOQyxZQUFZLEtBQUssRUFDakJDLFdBQVcsSUFBSSxFQUNmQyxPQUFPLElBQUksRUFDWEMsU0FBUyxFQUNUQyxXQUFXLEtBQUssRUFDQztJQUNqQixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR2QsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDZSxVQUFVQyxZQUFZLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNpQixhQUFhQyxlQUFlLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNtQixhQUFhQyxlQUFlLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNcUIsY0FBY3BCLDZDQUFNQSxDQUFpQjtJQUUzQyxVQUFVO0lBQ1YsTUFBTXFCLGFBQWEsQ0FBQ0M7UUFDbEIsSUFBSSxDQUFDQyxTQUFTRCxZQUFZQSxVQUFVLEdBQUcsT0FBTztRQUU5QyxNQUFNRSxPQUFPQyxLQUFLQyxLQUFLLENBQUNKLFVBQVU7UUFDbEMsTUFBTUssT0FBT0YsS0FBS0MsS0FBSyxDQUFDSixVQUFVO1FBQ2xDLE9BQU8sQ0FBQyxFQUFFRSxLQUFLLENBQUMsRUFBRUcsS0FBS0MsUUFBUSxHQUFHQyxRQUFRLENBQUMsR0FBRyxLQUFLLENBQUM7SUFDdEQ7SUFFQSxVQUFVO0lBQ1YsTUFBTUMsV0FBV3pCLFdBQVcsSUFBSSxjQUFlQSxXQUFZLE1BQU07SUFDakUsTUFBTTBCLGNBQWNuQixhQUFhRSxXQUFXVjtJQUM1QyxNQUFNNEIsa0JBQWtCcEIsYUFBYSxXQUFZUCxXQUFZLE1BQU15QjtJQUVuRSxTQUFTO0lBQ1QsTUFBTUcsb0JBQW9CLENBQUNDO1FBQ3pCLElBQUl2QixZQUFZTixhQUFhLEdBQUc7UUFFaENRLGNBQWM7UUFDZHNCLG9CQUFvQkQ7UUFDcEJBLE1BQU1FLGNBQWM7SUFDdEI7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTUMsa0JBQWtCLENBQUNIO1FBQ3ZCLElBQUksQ0FBQ2QsWUFBWWtCLE9BQU8sSUFBSWpDLGFBQWEsR0FBRztRQUU1QyxNQUFNa0MsT0FBT25CLFlBQVlrQixPQUFPLENBQUNFLHFCQUFxQjtRQUN0RCxNQUFNQyxJQUFJUCxNQUFNUSxPQUFPLEdBQUdILEtBQUtJLElBQUk7UUFDbkMsTUFBTUMsYUFBYW5CLEtBQUtvQixHQUFHLENBQUMsR0FBR3BCLEtBQUtxQixHQUFHLENBQUMsR0FBR0wsSUFBSUYsS0FBS1EsS0FBSztRQUN6RCxNQUFNQyxPQUFPSixhQUFhdkM7UUFFMUJjLGVBQWU2QjtRQUNmL0IsZUFBZTtJQUNqQjtJQUVBLFNBQVM7SUFDVCxNQUFNZ0MsbUJBQW1CO1FBQ3ZCLElBQUksQ0FBQ3JDLFlBQVk7WUFDZkssZUFBZTtRQUNqQjtJQUNGO0lBRUEsVUFBVTtJQUNWLE1BQU1rQixzQkFBc0IsQ0FBQ0Q7UUFDM0IsSUFBSSxDQUFDZCxZQUFZa0IsT0FBTyxJQUFJakMsYUFBYSxHQUFHO1FBRTVDLE1BQU1rQyxPQUFPbkIsWUFBWWtCLE9BQU8sQ0FBQ0UscUJBQXFCO1FBQ3RELE1BQU1DLElBQUlQLE1BQU1RLE9BQU8sR0FBR0gsS0FBS0ksSUFBSTtRQUNuQyxNQUFNQyxhQUFhbkIsS0FBS29CLEdBQUcsQ0FBQyxHQUFHcEIsS0FBS3FCLEdBQUcsQ0FBQyxHQUFHTCxJQUFJRixLQUFLUSxLQUFLO1FBQ3pELE1BQU1DLE9BQU9KLGFBQWF2QztRQUUxQlUsWUFBWWlDO1FBQ1o3QixlQUFlNkI7SUFDakI7SUFFQSxTQUFTO0lBQ1QsTUFBTUUsb0JBQW9CLENBQUNoQjtRQUN6QixJQUFJLENBQUN0QixjQUFjRCxVQUFVO1FBQzdCd0Isb0JBQW9CRDtJQUN0QjtJQUVBLFNBQVM7SUFDVCxNQUFNaUIsa0JBQWtCO1FBQ3RCLElBQUl2QyxZQUFZO1lBQ2ROLE9BQU9RO1lBQ1BELGNBQWM7WUFDZEksZUFBZTtRQUNqQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1tQyxjQUFjLENBQUNsQjtRQUNuQixJQUFJdkIsWUFBWU4sYUFBYSxLQUFLTyxZQUFZO1FBRTlDLE1BQU0yQixPQUFPbkIsWUFBWWtCLE9BQU8sRUFBRUU7UUFDbEMsSUFBSSxDQUFDRCxNQUFNO1FBRVgsTUFBTUUsSUFBSVAsTUFBTVEsT0FBTyxHQUFHSCxLQUFLSSxJQUFJO1FBQ25DLE1BQU1DLGFBQWFuQixLQUFLb0IsR0FBRyxDQUFDLEdBQUdwQixLQUFLcUIsR0FBRyxDQUFDLEdBQUdMLElBQUlGLEtBQUtRLEtBQUs7UUFDekQsTUFBTUMsT0FBT0osYUFBYXZDO1FBRTFCQyxPQUFPMEM7SUFDVDtJQUVBLE9BQU87SUFDUCxNQUFNSyxnQkFBZ0IsQ0FBQ25CO1FBQ3JCLElBQUl2QixZQUFZTixhQUFhLEdBQUc7UUFFaEMsSUFBSWlELFVBQVVsRDtRQUNkLE1BQU1tRCxPQUFPbEQsV0FBVyxNQUFNLFFBQVE7UUFFdEMsT0FBUTZCLE1BQU1zQixHQUFHO1lBQ2YsS0FBSztnQkFDSEYsVUFBVTdCLEtBQUtvQixHQUFHLENBQUMsR0FBR3pDLGNBQWNtRDtnQkFDcEM7WUFDRixLQUFLO2dCQUNIRCxVQUFVN0IsS0FBS3FCLEdBQUcsQ0FBQ3pDLFVBQVVELGNBQWNtRDtnQkFDM0M7WUFDRixLQUFLO2dCQUNIRCxVQUFVO2dCQUNWO1lBQ0YsS0FBSztnQkFDSEEsVUFBVWpEO2dCQUNWO1lBQ0Y7Z0JBQ0U7UUFDSjtRQUVBNkIsTUFBTUUsY0FBYztRQUNwQjlCLE9BQU9nRDtJQUNUO0lBRUEsV0FBVztJQUNYckQsZ0RBQVNBLENBQUM7UUFDUixJQUFJVyxZQUFZO1lBQ2Q2QyxTQUFTQyxnQkFBZ0IsQ0FBQyxlQUFlUjtZQUN6Q08sU0FBU0MsZ0JBQWdCLENBQUMsYUFBYVA7WUFFdkMsT0FBTztnQkFDTE0sU0FBU0UsbUJBQW1CLENBQUMsZUFBZVQ7Z0JBQzVDTyxTQUFTRSxtQkFBbUIsQ0FBQyxhQUFhUjtZQUM1QztRQUNGO0lBQ0YsR0FBRztRQUFDdkM7UUFBWUU7S0FBUztJQUV6QixNQUFNOEMsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUVBLE1BQU1DLFlBQVk7UUFDaEJILElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFFQSxxQkFDRSw4REFBQ0U7UUFBSXZELFdBQVdSLDBDQUFJQSxDQUFDLFVBQVVROztZQUU1QkYsMEJBQ0MsOERBQUN5RDtnQkFBSXZELFdBQVU7O2tDQUNiLDhEQUFDd0Q7d0JBQUt4RCxXQUFVO2tDQUNiVyxXQUFXVTs7Ozs7O2tDQUVkLDhEQUFDbUM7d0JBQUt4RCxXQUFVO2tDQUNiVyxXQUFXaEI7Ozs7Ozs7Ozs7OzswQkFNbEIsOERBQUM0RDtnQkFBSXZELFdBQVU7O2tDQUNiLDhEQUFDdUQ7d0JBQ0NFLEtBQUsvQzt3QkFDTFYsV0FBV1IsMENBQUlBLENBQ2IsMkVBQ0Esd0RBQ0EwRCxXQUFXLENBQUNuRCxLQUFLLEVBQ2pCRSxZQUFZO3dCQUVkeUQsZUFBZW5DO3dCQUNmb0MsU0FBU2pCO3dCQUNUa0IsYUFBYWpDO3dCQUNia0MsY0FBY3RCO3dCQUNkdUIsV0FBV25CO3dCQUNYb0IsVUFBVTlELFdBQVcsQ0FBQyxJQUFJO3dCQUMxQitELE1BQUs7d0JBQ0xDLGlCQUFlO3dCQUNmQyxpQkFBZXZFO3dCQUNmd0UsaUJBQWV6RTt3QkFDZjBFLGNBQVc7OzRCQUdWdkUsMkJBQ0MsOERBQUMwRDtnQ0FBSXZELFdBQVU7Ozs7OzswQ0FJakIsOERBQUN1RDtnQ0FDQ3ZELFdBQVdSLDBDQUFJQSxDQUNiLCtFQUNBMEQsV0FBVyxDQUFDbkQsS0FBSztnQ0FFbkJzRSxPQUFPO29DQUFFaEMsT0FBTyxDQUFDLEVBQUV0QixLQUFLb0IsR0FBRyxDQUFDLEdBQUdwQixLQUFLcUIsR0FBRyxDQUFDLEtBQUtkLGtCQUFrQixDQUFDLENBQUM7Z0NBQUM7Ozs7Ozs0QkFPbkUzQixXQUFXLG1CQUNWLDhEQUFDNEQ7Z0NBQ0N2RCxXQUFXUiwwQ0FBSUEsQ0FDYiw4RUFDQSwyRUFDQSxxQ0FDQVUsY0FBYyx5QkFDZG9ELFNBQVMsQ0FBQ3ZELEtBQUs7Z0NBRWpCc0UsT0FBTztvQ0FBRXBDLE1BQU0sQ0FBQyxFQUFFbEIsS0FBS29CLEdBQUcsQ0FBQyxHQUFHcEIsS0FBS3FCLEdBQUcsQ0FBQyxLQUFLZCxrQkFBa0IsQ0FBQyxDQUFDO2dDQUFDOzs7Ozs7Ozs7Ozs7b0JBTXRFaEIsZUFBZVgsV0FBVyxtQkFDekIsOERBQUM0RDt3QkFDQ3ZELFdBQVU7d0JBQ1ZxRSxPQUFPOzRCQUNMcEMsTUFBTSxDQUFDLEVBQUVsQixLQUFLb0IsR0FBRyxDQUFDLEdBQUdwQixLQUFLcUIsR0FBRyxDQUFDLEtBQUssY0FBZXpDLFdBQVksTUFBTSxDQUFDLENBQUM7d0JBQ3hFO2tDQUVDZ0IsV0FBV0g7Ozs7Ozs7Ozs7OztZQU1qQmIsV0FBVyxtQkFDViw4REFBQzREO2dCQUFJdkQsV0FBVTs7b0JBQ1plLEtBQUt1RCxLQUFLLENBQUNoRDtvQkFBaUI7Ozs7Ozs7Ozs7Ozs7QUFLdkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub2lzZXNsZWVwLXdlYi8uL3NyYy9jb21wb25lbnRzL0F1ZGlvUGxheWVyL1Byb2dyZXNzQmFyLnRzeD9jNTM0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY2xzeCB9IGZyb20gJ2Nsc3gnO1xuXG5pbnRlcmZhY2UgUHJvZ3Jlc3NCYXJQcm9wcyB7XG4gIGN1cnJlbnRUaW1lOiBudW1iZXI7XG4gIGR1cmF0aW9uOiBudW1iZXI7XG4gIG9uU2VlazogKHRpbWU6IG51bWJlcikgPT4gdm9pZDtcbiAgaXNMb2FkaW5nPzogYm9vbGVhbjtcbiAgc2hvd1RpbWU/OiBib29sZWFuO1xuICBzaXplPzogJ3NtJyB8ICdtZCcgfCAnbGcnO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIGRpc2FibGVkPzogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb2dyZXNzQmFyKHtcbiAgY3VycmVudFRpbWUsXG4gIGR1cmF0aW9uLFxuICBvblNlZWssXG4gIGlzTG9hZGluZyA9IGZhbHNlLFxuICBzaG93VGltZSA9IHRydWUsXG4gIHNpemUgPSAnbWQnLFxuICBjbGFzc05hbWUsXG4gIGRpc2FibGVkID0gZmFsc2UsXG59OiBQcm9ncmVzc0JhclByb3BzKSB7XG4gIGNvbnN0IFtpc0RyYWdnaW5nLCBzZXRJc0RyYWdnaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2RyYWdUaW1lLCBzZXREcmFnVGltZV0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW3Nob3dUb29sdGlwLCBzZXRTaG93VG9vbHRpcF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFt0b29sdGlwVGltZSwgc2V0VG9vbHRpcFRpbWVdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IHByb2dyZXNzUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICAvLyDmoLzlvI/ljJbml7bpl7TmmL7npLpcbiAgY29uc3QgZm9ybWF0VGltZSA9IChzZWNvbmRzOiBudW1iZXIpOiBzdHJpbmcgPT4ge1xuICAgIGlmICghaXNGaW5pdGUoc2Vjb25kcykgfHwgc2Vjb25kcyA8IDApIHJldHVybiAnMDowMCc7XG4gICAgXG4gICAgY29uc3QgbWlucyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDYwKTtcbiAgICBjb25zdCBzZWNzID0gTWF0aC5mbG9vcihzZWNvbmRzICUgNjApO1xuICAgIHJldHVybiBgJHttaW5zfToke3NlY3MudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfWA7XG4gIH07XG5cbiAgLy8g6K6h566X6L+b5bqm55m+5YiG5q+UXG4gIGNvbnN0IHByb2dyZXNzID0gZHVyYXRpb24gPiAwID8gKGN1cnJlbnRUaW1lIC8gZHVyYXRpb24pICogMTAwIDogMDtcbiAgY29uc3QgZGlzcGxheVRpbWUgPSBpc0RyYWdnaW5nID8gZHJhZ1RpbWUgOiBjdXJyZW50VGltZTtcbiAgY29uc3QgZGlzcGxheVByb2dyZXNzID0gaXNEcmFnZ2luZyA/IChkcmFnVGltZSAvIGR1cmF0aW9uKSAqIDEwMCA6IHByb2dyZXNzO1xuXG4gIC8vIOWkhOeQhuaLluaLveW8gOWni1xuICBjb25zdCBoYW5kbGVQb2ludGVyRG93biA9IChldmVudDogUmVhY3QuUG9pbnRlckV2ZW50KSA9PiB7XG4gICAgaWYgKGRpc2FibGVkIHx8IGR1cmF0aW9uID09PSAwKSByZXR1cm47XG4gICAgXG4gICAgc2V0SXNEcmFnZ2luZyh0cnVlKTtcbiAgICB1cGRhdGVUaW1lRnJvbUV2ZW50KGV2ZW50KTtcbiAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICB9O1xuXG4gIC8vIOWkhOeQhum8oOagh+enu+WKqO+8iOeUqOS6juaYvuekuumihOiniOaXtumXtO+8iVxuICBjb25zdCBoYW5kbGVNb3VzZU1vdmUgPSAoZXZlbnQ6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcbiAgICBpZiAoIXByb2dyZXNzUmVmLmN1cnJlbnQgfHwgZHVyYXRpb24gPT09IDApIHJldHVybjtcbiAgICBcbiAgICBjb25zdCByZWN0ID0gcHJvZ3Jlc3NSZWYuY3VycmVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICBjb25zdCB4ID0gZXZlbnQuY2xpZW50WCAtIHJlY3QubGVmdDtcbiAgICBjb25zdCBwZXJjZW50YWdlID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oMSwgeCAvIHJlY3Qud2lkdGgpKTtcbiAgICBjb25zdCB0aW1lID0gcGVyY2VudGFnZSAqIGR1cmF0aW9uO1xuICAgIFxuICAgIHNldFRvb2x0aXBUaW1lKHRpbWUpO1xuICAgIHNldFNob3dUb29sdGlwKHRydWUpO1xuICB9O1xuXG4gIC8vIOWkhOeQhum8oOagh+emu+W8gFxuICBjb25zdCBoYW5kbGVNb3VzZUxlYXZlID0gKCkgPT4ge1xuICAgIGlmICghaXNEcmFnZ2luZykge1xuICAgICAgc2V0U2hvd1Rvb2x0aXAoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDku47kuovku7bmm7TmlrDml7bpl7RcbiAgY29uc3QgdXBkYXRlVGltZUZyb21FdmVudCA9IChldmVudDogUG9pbnRlckV2ZW50IHwgUmVhY3QuUG9pbnRlckV2ZW50KSA9PiB7XG4gICAgaWYgKCFwcm9ncmVzc1JlZi5jdXJyZW50IHx8IGR1cmF0aW9uID09PSAwKSByZXR1cm47XG5cbiAgICBjb25zdCByZWN0ID0gcHJvZ3Jlc3NSZWYuY3VycmVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICBjb25zdCB4ID0gZXZlbnQuY2xpZW50WCAtIHJlY3QubGVmdDtcbiAgICBjb25zdCBwZXJjZW50YWdlID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oMSwgeCAvIHJlY3Qud2lkdGgpKTtcbiAgICBjb25zdCB0aW1lID0gcGVyY2VudGFnZSAqIGR1cmF0aW9uO1xuICAgIFxuICAgIHNldERyYWdUaW1lKHRpbWUpO1xuICAgIHNldFRvb2x0aXBUaW1lKHRpbWUpO1xuICB9O1xuXG4gIC8vIOWkhOeQhuaLluaLveenu+WKqFxuICBjb25zdCBoYW5kbGVQb2ludGVyTW92ZSA9IChldmVudDogUG9pbnRlckV2ZW50KSA9PiB7XG4gICAgaWYgKCFpc0RyYWdnaW5nIHx8IGRpc2FibGVkKSByZXR1cm47XG4gICAgdXBkYXRlVGltZUZyb21FdmVudChldmVudCk7XG4gIH07XG5cbiAgLy8g5aSE55CG5ouW5ou957uT5p2fXG4gIGNvbnN0IGhhbmRsZVBvaW50ZXJVcCA9ICgpID0+IHtcbiAgICBpZiAoaXNEcmFnZ2luZykge1xuICAgICAgb25TZWVrKGRyYWdUaW1lKTtcbiAgICAgIHNldElzRHJhZ2dpbmcoZmFsc2UpO1xuICAgICAgc2V0U2hvd1Rvb2x0aXAoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlpITnkIbngrnlh7vot7PovaxcbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSAoZXZlbnQ6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcbiAgICBpZiAoZGlzYWJsZWQgfHwgZHVyYXRpb24gPT09IDAgfHwgaXNEcmFnZ2luZykgcmV0dXJuO1xuICAgIFxuICAgIGNvbnN0IHJlY3QgPSBwcm9ncmVzc1JlZi5jdXJyZW50Py5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICBpZiAoIXJlY3QpIHJldHVybjtcbiAgICBcbiAgICBjb25zdCB4ID0gZXZlbnQuY2xpZW50WCAtIHJlY3QubGVmdDtcbiAgICBjb25zdCBwZXJjZW50YWdlID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oMSwgeCAvIHJlY3Qud2lkdGgpKTtcbiAgICBjb25zdCB0aW1lID0gcGVyY2VudGFnZSAqIGR1cmF0aW9uO1xuICAgIFxuICAgIG9uU2Vlayh0aW1lKTtcbiAgfTtcblxuICAvLyDplK7nm5jmjqfliLZcbiAgY29uc3QgaGFuZGxlS2V5RG93biA9IChldmVudDogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgIGlmIChkaXNhYmxlZCB8fCBkdXJhdGlvbiA9PT0gMCkgcmV0dXJuO1xuXG4gICAgbGV0IG5ld1RpbWUgPSBjdXJyZW50VGltZTtcbiAgICBjb25zdCBzdGVwID0gZHVyYXRpb24gKiAwLjA1OyAvLyA1JSDmraXplb9cblxuICAgIHN3aXRjaCAoZXZlbnQua2V5KSB7XG4gICAgICBjYXNlICdBcnJvd0xlZnQnOlxuICAgICAgICBuZXdUaW1lID0gTWF0aC5tYXgoMCwgY3VycmVudFRpbWUgLSBzdGVwKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdBcnJvd1JpZ2h0JzpcbiAgICAgICAgbmV3VGltZSA9IE1hdGgubWluKGR1cmF0aW9uLCBjdXJyZW50VGltZSArIHN0ZXApO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ0hvbWUnOlxuICAgICAgICBuZXdUaW1lID0gMDtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdFbmQnOlxuICAgICAgICBuZXdUaW1lID0gZHVyYXRpb247XG4gICAgICAgIGJyZWFrO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgb25TZWVrKG5ld1RpbWUpO1xuICB9O1xuXG4gIC8vIOebkeWQrOWFqOWxgOm8oOagh+S6i+S7tlxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc0RyYWdnaW5nKSB7XG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdwb2ludGVybW92ZScsIGhhbmRsZVBvaW50ZXJNb3ZlKTtcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3BvaW50ZXJ1cCcsIGhhbmRsZVBvaW50ZXJVcCk7XG4gICAgICBcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3BvaW50ZXJtb3ZlJywgaGFuZGxlUG9pbnRlck1vdmUpO1xuICAgICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdwb2ludGVydXAnLCBoYW5kbGVQb2ludGVyVXApO1xuICAgICAgfTtcbiAgICB9XG4gIH0sIFtpc0RyYWdnaW5nLCBkcmFnVGltZV0pO1xuXG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtOiAnaC0xJyxcbiAgICBtZDogJ2gtMicsXG4gICAgbGc6ICdoLTMnLFxuICB9O1xuXG4gIGNvbnN0IHRodW1iU2l6ZSA9IHtcbiAgICBzbTogJ3ctMyBoLTMnLFxuICAgIG1kOiAndy00IGgtNCcsXG4gICAgbGc6ICd3LTUgaC01JyxcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbHN4KCd3LWZ1bGwnLCBjbGFzc05hbWUpfT5cbiAgICAgIHsvKiDml7bpl7TmmL7npLogKi99XG4gICAgICB7c2hvd1RpbWUgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi0yIHRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1vbm9cIj5cbiAgICAgICAgICAgIHtmb3JtYXRUaW1lKGRpc3BsYXlUaW1lKX1cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tb25vXCI+XG4gICAgICAgICAgICB7Zm9ybWF0VGltZShkdXJhdGlvbil9XG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDov5vluqbmnaHlrrnlmaggKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgIDxkaXZcbiAgICAgICAgICByZWY9e3Byb2dyZXNzUmVmfVxuICAgICAgICAgIGNsYXNzTmFtZT17Y2xzeChcbiAgICAgICAgICAgICdyZWxhdGl2ZSBiZy1ncmF5LTIwMCBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWQtZnVsbCBjdXJzb3ItcG9pbnRlciBncm91cCcsXG4gICAgICAgICAgICAnZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWFtYmVyLTUwMCcsXG4gICAgICAgICAgICBzaXplQ2xhc3Nlc1tzaXplXSxcbiAgICAgICAgICAgIGRpc2FibGVkICYmICdvcGFjaXR5LTUwIGN1cnNvci1ub3QtYWxsb3dlZCdcbiAgICAgICAgICApfVxuICAgICAgICAgIG9uUG9pbnRlckRvd249e2hhbmRsZVBvaW50ZXJEb3dufVxuICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsaWNrfVxuICAgICAgICAgIG9uTW91c2VNb3ZlPXtoYW5kbGVNb3VzZU1vdmV9XG4gICAgICAgICAgb25Nb3VzZUxlYXZlPXtoYW5kbGVNb3VzZUxlYXZlfVxuICAgICAgICAgIG9uS2V5RG93bj17aGFuZGxlS2V5RG93bn1cbiAgICAgICAgICB0YWJJbmRleD17ZGlzYWJsZWQgPyAtMSA6IDB9XG4gICAgICAgICAgcm9sZT1cInNsaWRlclwiXG4gICAgICAgICAgYXJpYS12YWx1ZW1pbj17MH1cbiAgICAgICAgICBhcmlhLXZhbHVlbWF4PXtkdXJhdGlvbn1cbiAgICAgICAgICBhcmlhLXZhbHVlbm93PXtjdXJyZW50VGltZX1cbiAgICAgICAgICBhcmlhLWxhYmVsPVwi6Z+z6aKR6L+b5bqmXCJcbiAgICAgICAgPlxuICAgICAgICAgIHsvKiDliqDovb3nirbmgIEgKi99XG4gICAgICAgICAge2lzTG9hZGluZyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JheS0zMDAgZGFyazpiZy1ncmF5LTYwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZVwiIC8+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiDlt7Lmkq3mlL7ov5vluqYgKi99XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPXtjbHN4KFxuICAgICAgICAgICAgICAnYWJzb2x1dGUgbGVmdC0wIHRvcC0wIGJnLWFtYmVyLTUwMCByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMTUwJyxcbiAgICAgICAgICAgICAgc2l6ZUNsYXNzZXNbc2l6ZV1cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7TWF0aC5tYXgoMCwgTWF0aC5taW4oMTAwLCBkaXNwbGF5UHJvZ3Jlc3MpKX0lYCB9fVxuICAgICAgICAgIC8+XG5cbiAgICAgICAgICB7Lyog57yT5Yay6L+b5bqm77yI5aaC5p6c6ZyA6KaB55qE6K+d77yJICovfVxuICAgICAgICAgIHsvKiDov5nph4zlj6/ku6Xmt7vliqDnvJPlhrLov5vluqbmmL7npLogKi99XG5cbiAgICAgICAgICB7Lyog5ouW5ou95omL5p+EICovfVxuICAgICAgICAgIHtkdXJhdGlvbiA+IDAgJiYgKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Nsc3goXG4gICAgICAgICAgICAgICAgJ2Fic29sdXRlIHRvcC0xLzIgYmctd2hpdGUgYm9yZGVyLTIgYm9yZGVyLWFtYmVyLTUwMCByb3VuZGVkLWZ1bGwgc2hhZG93LW1kJyxcbiAgICAgICAgICAgICAgICAndHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgLXRyYW5zbGF0ZS15LTEvMiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0xNTAnLFxuICAgICAgICAgICAgICAgICdvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAnLFxuICAgICAgICAgICAgICAgIGlzRHJhZ2dpbmcgJiYgJ29wYWNpdHktMTAwIHNjYWxlLTEyNScsXG4gICAgICAgICAgICAgICAgdGh1bWJTaXplW3NpemVdXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIHN0eWxlPXt7IGxlZnQ6IGAke01hdGgubWF4KDAsIE1hdGgubWluKDEwMCwgZGlzcGxheVByb2dyZXNzKSl9JWAgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOaXtumXtOaPkOekuiAqL31cbiAgICAgICAge3Nob3dUb29sdGlwICYmIGR1cmF0aW9uID4gMCAmJiAoXG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgei0xMCBweC0yIHB5LTEgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIGJnLWdyYXktOTAwIHJvdW5kZWQgc2hhZG93LWxnIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXgtMS8yIC10b3AtOFwiXG4gICAgICAgICAgICBzdHlsZT17eyBcbiAgICAgICAgICAgICAgbGVmdDogYCR7TWF0aC5tYXgoMCwgTWF0aC5taW4oMTAwLCAodG9vbHRpcFRpbWUgLyBkdXJhdGlvbikgKiAxMDApKX0lYCBcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAge2Zvcm1hdFRpbWUodG9vbHRpcFRpbWUpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDov5vluqbnmb7liIbmr5TvvIjlj6/pgInvvIkgKi99XG4gICAgICB7ZHVyYXRpb24gPiAwICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xIHRleHQteHMgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICB7TWF0aC5yb3VuZChkaXNwbGF5UHJvZ3Jlc3MpfSVcbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiY2xzeCIsIlByb2dyZXNzQmFyIiwiY3VycmVudFRpbWUiLCJkdXJhdGlvbiIsIm9uU2VlayIsImlzTG9hZGluZyIsInNob3dUaW1lIiwic2l6ZSIsImNsYXNzTmFtZSIsImRpc2FibGVkIiwiaXNEcmFnZ2luZyIsInNldElzRHJhZ2dpbmciLCJkcmFnVGltZSIsInNldERyYWdUaW1lIiwic2hvd1Rvb2x0aXAiLCJzZXRTaG93VG9vbHRpcCIsInRvb2x0aXBUaW1lIiwic2V0VG9vbHRpcFRpbWUiLCJwcm9ncmVzc1JlZiIsImZvcm1hdFRpbWUiLCJzZWNvbmRzIiwiaXNGaW5pdGUiLCJtaW5zIiwiTWF0aCIsImZsb29yIiwic2VjcyIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJwcm9ncmVzcyIsImRpc3BsYXlUaW1lIiwiZGlzcGxheVByb2dyZXNzIiwiaGFuZGxlUG9pbnRlckRvd24iLCJldmVudCIsInVwZGF0ZVRpbWVGcm9tRXZlbnQiLCJwcmV2ZW50RGVmYXVsdCIsImhhbmRsZU1vdXNlTW92ZSIsImN1cnJlbnQiLCJyZWN0IiwiZ2V0Qm91bmRpbmdDbGllbnRSZWN0IiwieCIsImNsaWVudFgiLCJsZWZ0IiwicGVyY2VudGFnZSIsIm1heCIsIm1pbiIsIndpZHRoIiwidGltZSIsImhhbmRsZU1vdXNlTGVhdmUiLCJoYW5kbGVQb2ludGVyTW92ZSIsImhhbmRsZVBvaW50ZXJVcCIsImhhbmRsZUNsaWNrIiwiaGFuZGxlS2V5RG93biIsIm5ld1RpbWUiLCJzdGVwIiwia2V5IiwiZG9jdW1lbnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInNpemVDbGFzc2VzIiwic20iLCJtZCIsImxnIiwidGh1bWJTaXplIiwiZGl2Iiwic3BhbiIsInJlZiIsIm9uUG9pbnRlckRvd24iLCJvbkNsaWNrIiwib25Nb3VzZU1vdmUiLCJvbk1vdXNlTGVhdmUiLCJvbktleURvd24iLCJ0YWJJbmRleCIsInJvbGUiLCJhcmlhLXZhbHVlbWluIiwiYXJpYS12YWx1ZW1heCIsImFyaWEtdmFsdWVub3ciLCJhcmlhLWxhYmVsIiwic3R5bGUiLCJyb3VuZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/ProgressBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/StandardPlayer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/AudioPlayer/StandardPlayer.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StandardPlayer: () => (/* binding */ StandardPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/audioStore */ \"(ssr)/./src/store/audioStore.ts\");\n/* harmony import */ var _hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAudioPlayer */ \"(ssr)/./src/hooks/useAudioPlayer.ts\");\n/* harmony import */ var _PlayButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PlayButton */ \"(ssr)/./src/components/AudioPlayer/PlayButton.tsx\");\n/* harmony import */ var _VolumeControl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VolumeControl */ \"(ssr)/./src/components/AudioPlayer/VolumeControl.tsx\");\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ProgressBar */ \"(ssr)/./src/components/AudioPlayer/ProgressBar.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SpeakerWaveIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MoonIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ StandardPlayer auto */ \n\n\n\n\n\n\n\n\n\n\nfunction StandardPlayer({ className, position = \"bottom\", showMixingButton = true, showSleepModeButton = true, autoHide = false, autoHideDelay = 5000 }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"audioPlayer\");\n    const tCommon = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"common\");\n    const { currentSound, playerUI, favorites, setPlayerVisible, setPlayerMode, togglePlayerMinimized, setTimerPanelVisible, setMixingPanelVisible, addToFavorites, removeFromFavorites } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_3__.useAudioStore)();\n    const { play, pause, stop, setVolume, seek, isPlaying, isPaused, isLoading, currentTime, duration, volume, error } = (0,_hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__.useAudioPlayer)();\n    // 当有音频播放时自动显示播放器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentSound && !playerUI.isVisible) {\n            setPlayerVisible(true);\n        }\n    }, [\n        currentSound,\n        playerUI.isVisible,\n        setPlayerVisible\n    ]);\n    // 自动隐藏功能\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (autoHide && !isPlaying && playerUI.isVisible) {\n            const timer = setTimeout(()=>{\n                setPlayerVisible(false);\n            }, autoHideDelay);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        autoHide,\n        isPlaying,\n        playerUI.isVisible,\n        autoHideDelay,\n        setPlayerVisible\n    ]);\n    // 播放控制处理\n    const handlePlay = ()=>{\n        if (currentSound) {\n            play();\n        }\n    };\n    const handlePause = ()=>{\n        pause();\n    };\n    const handleStop = ()=>{\n        stop();\n        setPlayerVisible(false);\n    };\n    const handleVolumeChange = (newVolume)=>{\n        setVolume(newVolume);\n    };\n    const handleSeek = (position)=>{\n        seek(position);\n    };\n    // 收藏控制\n    const isFavorite = currentSound ? favorites.includes(currentSound.id) : false;\n    const handleToggleFavorite = ()=>{\n        if (currentSound) {\n            if (isFavorite) {\n                removeFromFavorites(currentSound.id);\n            } else {\n                addToFavorites(currentSound.id);\n            }\n        }\n    };\n    // 面板控制\n    const handleTimerClick = ()=>{\n        setTimerPanelVisible(!playerUI.showTimerPanel);\n    };\n    const handleMixingClick = ()=>{\n        setMixingPanelVisible(!playerUI.showMixingPanel);\n    };\n    const handleSleepModeClick = ()=>{\n        setPlayerMode(\"sleep\");\n    };\n    const handleMinimize = ()=>{\n        togglePlayerMinimized();\n    };\n    const handleClose = ()=>{\n        stop();\n        setPlayerVisible(false);\n    };\n    // 位置样式 - 响应式优化\n    const positionClasses = {\n        bottom: \"fixed bottom-0 left-0 right-0 z-50\",\n        top: \"fixed top-0 left-0 right-0 z-50\",\n        floating: \"fixed bottom-4 left-2 right-2 sm:left-4 sm:right-4 z-50 max-w-4xl mx-auto\"\n    };\n    // 动画变体\n    const playerVariants = {\n        hidden: {\n            y: position === \"bottom\" ? 100 : position === \"top\" ? -100 : 20,\n            opacity: 0\n        },\n        visible: {\n            y: 0,\n            opacity: 1,\n            transition: {\n                type: \"spring\",\n                stiffness: 300,\n                damping: 30\n            }\n        },\n        exit: {\n            y: position === \"bottom\" ? 100 : position === \"top\" ? -100 : 20,\n            opacity: 0,\n            transition: {\n                duration: 0.2\n            }\n        }\n    };\n    const minimizedVariants = {\n        normal: {\n            height: \"auto\"\n        },\n        minimized: {\n            height: 60,\n            transition: {\n                duration: 0.3\n            }\n        }\n    };\n    if (!playerUI.isVisible || !currentSound) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n            initial: \"hidden\",\n            animate: \"visible\",\n            exit: \"exit\",\n            variants: playerVariants,\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(positionClasses[position], \"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md\", \"border-t border-gray-200 dark:border-gray-700\", position === \"floating\" && \"rounded-lg border shadow-xl\", className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                animate: playerUI.isMinimized ? \"minimized\" : \"normal\",\n                variants: minimizedVariants,\n                className: \"overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-3 sm:px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 sm:gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1 sm:gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PlayButton__WEBPACK_IMPORTED_MODULE_5__.PlayButton, {\n                                                        isPlaying: isPlaying,\n                                                        isLoading: isLoading,\n                                                        onPlay: handlePlay,\n                                                        onPause: handlePause,\n                                                        size: \"md\",\n                                                        variant: \"primary\",\n                                                        disabled: !currentSound\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleStop,\n                                                        disabled: !currentSound,\n                                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"disabled:opacity-50 disabled:cursor-not-allowed\", \"text-gray-600 dark:text-gray-400\"),\n                                                        \"aria-label\": t(\"controls.stop\"),\n                                                        title: t(\"controls.stop\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                        children: currentSound.title.zh || currentSound.title.en\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                        children: currentSound.description?.zh || currentSound.description?.en\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this),\n                                    !playerUI.isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressBar__WEBPACK_IMPORTED_MODULE_7__.ProgressBar, {\n                                            currentTime: currentTime,\n                                            duration: duration,\n                                            onSeek: handleSeek,\n                                            isLoading: isLoading,\n                                            showTime: true,\n                                            size: \"md\",\n                                            disabled: !currentSound\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 sm:gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VolumeControl__WEBPACK_IMPORTED_MODULE_6__.VolumeControl, {\n                                            volume: volume,\n                                            onVolumeChange: handleVolumeChange,\n                                            size: \"sm\",\n                                            showIcon: true,\n                                            disabled: !currentSound\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this),\n                                    !playerUI.isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleTimerClick,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"hidden sm:block p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", playerUI.showTimerPanel ? \"text-amber-600 bg-amber-50 dark:bg-amber-900/20\" : \"text-gray-600 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"timer.setTimer\"),\n                                                title: t(\"timer.setTimer\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 21\n                                            }, this),\n                                            showMixingButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleMixingClick,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"hidden sm:block p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", playerUI.showMixingPanel ? \"text-amber-600 bg-amber-50 dark:bg-amber-900/20\" : \"text-gray-600 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"mixing.title\"),\n                                                title: t(\"mixing.title\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 23\n                                            }, this),\n                                            showSleepModeButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSleepModeClick,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"text-gray-600 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"modes.switchToSleep\"),\n                                                title: t(\"modes.switchToSleep\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 ml-2 border-l border-gray-200 dark:border-gray-700 pl-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleMinimize,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1.5 rounded-md transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"text-gray-500 dark:text-gray-400\"),\n                                                \"aria-label\": playerUI.isMinimized ? t(\"controls.maximize\") : t(\"controls.minimize\"),\n                                                title: playerUI.isMinimized ? t(\"controls.maximize\") : t(\"controls.minimize\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleClose,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1.5 rounded-md transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"text-gray-500 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"controls.close\"),\n                                                title: t(\"controls.close\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n            lineNumber: 192,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/StandardPlayer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/VolumeControl.tsx":
/*!******************************************************!*\
  !*** ./src/components/AudioPlayer/VolumeControl.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VolumeControl: () => (/* binding */ VolumeControl)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ VolumeControl auto */ \n\n\n\nfunction VolumeControl({ volume, onVolumeChange, orientation = \"horizontal\", size = \"md\", showIcon = true, showValue = false, className, disabled = false }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)(\"common\");\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [previousVolume, setPreviousVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(volume);\n    // 处理音量变化\n    const handleVolumeChange = (newVolume)=>{\n        const clampedVolume = Math.max(0, Math.min(1, newVolume));\n        onVolumeChange(clampedVolume);\n    };\n    // 处理鼠标/触摸事件\n    const handlePointerDown = (event)=>{\n        if (disabled) return;\n        setIsDragging(true);\n        setShowTooltip(true);\n        updateVolumeFromEvent(event);\n        // 阻止默认行为\n        event.preventDefault();\n    };\n    const handlePointerMove = (event)=>{\n        if (!isDragging || disabled) return;\n        updateVolumeFromEvent(event);\n    };\n    const handlePointerUp = ()=>{\n        setIsDragging(false);\n        setShowTooltip(false);\n    };\n    // 从事件更新音量\n    const updateVolumeFromEvent = (event)=>{\n        if (!sliderRef.current) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        let newVolume;\n        if (orientation === \"horizontal\") {\n            const x = event.clientX - rect.left;\n            newVolume = x / rect.width;\n        } else {\n            const y = event.clientY - rect.top;\n            newVolume = 1 - y / rect.height; // 垂直方向反转\n        }\n        handleVolumeChange(newVolume);\n    };\n    // 键盘控制\n    const handleKeyDown = (event)=>{\n        if (disabled) return;\n        let newVolume = volume;\n        const step = 0.1;\n        switch(event.key){\n            case \"ArrowUp\":\n            case \"ArrowRight\":\n                newVolume = Math.min(1, volume + step);\n                break;\n            case \"ArrowDown\":\n            case \"ArrowLeft\":\n                newVolume = Math.max(0, volume - step);\n                break;\n            case \"Home\":\n                newVolume = 1;\n                break;\n            case \"End\":\n                newVolume = 0;\n                break;\n            default:\n                return;\n        }\n        event.preventDefault();\n        handleVolumeChange(newVolume);\n    };\n    // 静音切换\n    const toggleMute = ()=>{\n        if (disabled) return;\n        if (volume > 0) {\n            setPreviousVolume(volume);\n            handleVolumeChange(0);\n        } else {\n            handleVolumeChange(previousVolume > 0 ? previousVolume : 0.7);\n        }\n    };\n    // 监听全局鼠标事件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"pointermove\", handlePointerMove);\n            document.addEventListener(\"pointerup\", handlePointerUp);\n            return ()=>{\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                document.removeEventListener(\"pointerup\", handlePointerUp);\n            };\n        }\n    }, [\n        isDragging\n    ]);\n    // 获取音量图标\n    const getVolumeIcon = ()=>{\n        if (volume === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this);\n        } else if (volume < 0.3) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M7 9v6h4l5 5V4l-5 5H7z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this);\n        } else if (volume < 0.7) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this);\n        }\n    };\n    const sizeClasses = {\n        sm: orientation === \"horizontal\" ? \"h-1\" : \"w-1 h-16\",\n        md: orientation === \"horizontal\" ? \"h-2\" : \"w-2 h-20\",\n        lg: orientation === \"horizontal\" ? \"h-3\" : \"w-3 h-24\"\n    };\n    const thumbSize = {\n        sm: \"w-3 h-3\",\n        md: \"w-4 h-4\",\n        lg: \"w-5 h-5\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center gap-2\", orientation === \"vertical\" && \"flex-col\", className),\n        children: [\n            showIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleMute,\n                disabled: disabled,\n                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1 rounded-md transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"disabled:opacity-50 disabled:cursor-not-allowed\", volume === 0 ? \"text-red-500\" : \"text-gray-600 dark:text-gray-400\"),\n                \"aria-label\": volume === 0 ? \"取消静音\" : \"静音\",\n                title: volume === 0 ? \"取消静音\" : \"静音\",\n                children: getVolumeIcon()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: sliderRef,\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"relative bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", sizeClasses[size], disabled && \"opacity-50 cursor-not-allowed\"),\n                        onPointerDown: handlePointerDown,\n                        onKeyDown: handleKeyDown,\n                        tabIndex: disabled ? -1 : 0,\n                        role: \"slider\",\n                        \"aria-valuemin\": 0,\n                        \"aria-valuemax\": 100,\n                        \"aria-valuenow\": Math.round(volume * 100),\n                        \"aria-label\": t(\"volume\"),\n                        onMouseEnter: ()=>setShowTooltip(true),\n                        onMouseLeave: ()=>!isDragging && setShowTooltip(false),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bg-amber-500 rounded-full transition-all duration-150\",\n                                style: {\n                                    [orientation === \"horizontal\" ? \"width\" : \"height\"]: `${volume * 100}%`,\n                                    [orientation === \"horizontal\" ? \"height\" : \"width\"]: \"100%\",\n                                    [orientation === \"vertical\" ? \"bottom\" : \"left\"]: 0\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"absolute bg-white border-2 border-amber-500 rounded-full shadow-md\", \"transform -translate-x-1/2 -translate-y-1/2 transition-all duration-150\", \"hover:scale-110\", isDragging && \"scale-125\", thumbSize[size]),\n                                style: {\n                                    [orientation === \"horizontal\" ? \"left\" : \"bottom\"]: `${volume * 100}%`,\n                                    [orientation === \"horizontal\" ? \"top\" : \"left\"]: \"50%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    (showTooltip || showValue) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"absolute z-10 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg\", \"transform -translate-x-1/2\", orientation === \"horizontal\" ? \"-top-8 left-1/2\" : \"-right-12 top-1/2 -translate-y-1/2\"),\n                        children: [\n                            Math.round(volume * 100),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            showValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-medium text-gray-600 dark:text-gray-400 min-w-[3rem] text-right\",\n                children: [\n                    Math.round(volume * 100),\n                    \"%\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 262,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/VolumeControl.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/index.ts":
/*!*********************************************!*\
  !*** ./src/components/AudioPlayer/index.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioPlayer: () => (/* reexport safe */ _AudioPlayer__WEBPACK_IMPORTED_MODULE_0__.AudioPlayer),\n/* harmony export */   AudioPlayerProvider: () => (/* reexport safe */ _AudioPlayerProvider__WEBPACK_IMPORTED_MODULE_5__.AudioPlayerProvider),\n/* harmony export */   GhostPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.GhostPlayButton),\n/* harmony export */   LargePlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.LargePlayButton),\n/* harmony export */   PlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.PlayButton),\n/* harmony export */   PrimaryPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.PrimaryPlayButton),\n/* harmony export */   ProgressBar: () => (/* reexport safe */ _ProgressBar__WEBPACK_IMPORTED_MODULE_3__.ProgressBar),\n/* harmony export */   SecondaryPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.SecondaryPlayButton),\n/* harmony export */   SmallPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.SmallPlayButton),\n/* harmony export */   StandardPlayer: () => (/* reexport safe */ _StandardPlayer__WEBPACK_IMPORTED_MODULE_4__.StandardPlayer),\n/* harmony export */   VolumeControl: () => (/* reexport safe */ _VolumeControl__WEBPACK_IMPORTED_MODULE_2__.VolumeControl)\n/* harmony export */ });\n/* harmony import */ var _AudioPlayer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AudioPlayer */ \"(ssr)/./src/components/AudioPlayer/AudioPlayer.tsx\");\n/* harmony import */ var _PlayButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PlayButton */ \"(ssr)/./src/components/AudioPlayer/PlayButton.tsx\");\n/* harmony import */ var _VolumeControl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./VolumeControl */ \"(ssr)/./src/components/AudioPlayer/VolumeControl.tsx\");\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProgressBar */ \"(ssr)/./src/components/AudioPlayer/ProgressBar.tsx\");\n/* harmony import */ var _StandardPlayer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StandardPlayer */ \"(ssr)/./src/components/AudioPlayer/StandardPlayer.tsx\");\n/* harmony import */ var _AudioPlayerProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AudioPlayerProvider */ \"(ssr)/./src/components/AudioPlayer/AudioPlayerProvider.tsx\");\n// 主要组件\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BdWRpb1BsYXllci9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLE9BQU87QUFDcUM7QUFDeUY7QUFDckY7QUFDSjtBQUNNO0FBQ1UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub2lzZXNsZWVwLXdlYi8uL3NyYy9jb21wb25lbnRzL0F1ZGlvUGxheWVyL2luZGV4LnRzPzZhYzgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8g5Li76KaB57uE5Lu2XG5leHBvcnQgeyBBdWRpb1BsYXllciB9IGZyb20gJy4vQXVkaW9QbGF5ZXInO1xuZXhwb3J0IHsgUGxheUJ1dHRvbiwgUHJpbWFyeVBsYXlCdXR0b24sIFNlY29uZGFyeVBsYXlCdXR0b24sIEdob3N0UGxheUJ1dHRvbiwgTGFyZ2VQbGF5QnV0dG9uLCBTbWFsbFBsYXlCdXR0b24gfSBmcm9tICcuL1BsYXlCdXR0b24nO1xuZXhwb3J0IHsgVm9sdW1lQ29udHJvbCB9IGZyb20gJy4vVm9sdW1lQ29udHJvbCc7XG5leHBvcnQgeyBQcm9ncmVzc0JhciB9IGZyb20gJy4vUHJvZ3Jlc3NCYXInO1xuZXhwb3J0IHsgU3RhbmRhcmRQbGF5ZXIgfSBmcm9tICcuL1N0YW5kYXJkUGxheWVyJztcbmV4cG9ydCB7IEF1ZGlvUGxheWVyUHJvdmlkZXIgfSBmcm9tICcuL0F1ZGlvUGxheWVyUHJvdmlkZXInO1xuXG4vLyDnsbvlnovlrprkuYlcbmV4cG9ydCB0eXBlIHsgZGVmYXVsdCBhcyBBdWRpb1BsYXllclByb3BzIH0gZnJvbSAnLi9BdWRpb1BsYXllcic7XG5leHBvcnQgdHlwZSB7IGRlZmF1bHQgYXMgUGxheUJ1dHRvblByb3BzIH0gZnJvbSAnLi9QbGF5QnV0dG9uJztcbmV4cG9ydCB0eXBlIHsgZGVmYXVsdCBhcyBWb2x1bWVDb250cm9sUHJvcHMgfSBmcm9tICcuL1ZvbHVtZUNvbnRyb2wnO1xuZXhwb3J0IHR5cGUgeyBkZWZhdWx0IGFzIFByb2dyZXNzQmFyUHJvcHMgfSBmcm9tICcuL1Byb2dyZXNzQmFyJztcbiJdLCJuYW1lcyI6WyJBdWRpb1BsYXllciIsIlBsYXlCdXR0b24iLCJQcmltYXJ5UGxheUJ1dHRvbiIsIlNlY29uZGFyeVBsYXlCdXR0b24iLCJHaG9zdFBsYXlCdXR0b24iLCJMYXJnZVBsYXlCdXR0b24iLCJTbWFsbFBsYXlCdXR0b24iLCJWb2x1bWVDb250cm9sIiwiUHJvZ3Jlc3NCYXIiLCJTdGFuZGFyZFBsYXllciIsIkF1ZGlvUGxheWVyUHJvdmlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAudioPlayer.ts":
/*!*************************************!*\
  !*** ./src/hooks/useAudioPlayer.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioPlayer: () => (/* binding */ useAudioPlayer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var howler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! howler */ \"(ssr)/./node_modules/howler/dist/howler.js\");\n/* harmony import */ var howler__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(howler__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/audioStore */ \"(ssr)/./src/store/audioStore.ts\");\n\n\n\nconst useAudioPlayer = ()=>{\n    const howlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { currentSound, playState, userVolume, setCurrentSound, updatePlayState, setUserVolume } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_2__.useAudioStore)();\n    // 清理定时器\n    const clearProgressInterval = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n            intervalRef.current = null;\n        }\n    }, []);\n    // 开始进度更新定时器\n    const startProgressInterval = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        clearProgressInterval();\n        intervalRef.current = setInterval(()=>{\n            if (howlRef.current && howlRef.current.playing()) {\n                const currentTime = howlRef.current.seek();\n                updatePlayState({\n                    currentTime\n                });\n            }\n        }, 1000);\n    }, [\n        updatePlayState,\n        clearProgressInterval\n    ]);\n    // 播放音频\n    const play = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((sound)=>{\n        try {\n            // 如果传入了新的音频，先停止当前播放\n            if (sound && sound.id !== currentSound?.id) {\n                stop();\n                setCurrentSound(sound);\n                // 创建新的 Howl 实例\n                const audioUrl = `/Sounds/${sound.category}/${sound.filename}`;\n                howlRef.current = new howler__WEBPACK_IMPORTED_MODULE_1__.Howl({\n                    src: [\n                        audioUrl\n                    ],\n                    volume: userVolume,\n                    loop: playState.isLooping,\n                    onload: ()=>{\n                        updatePlayState({\n                            isLoading: false,\n                            duration: howlRef.current?.duration() || 0,\n                            error: null\n                        });\n                    },\n                    onplay: ()=>{\n                        updatePlayState({\n                            isPlaying: true,\n                            isPaused: false,\n                            error: null\n                        });\n                        startProgressInterval();\n                    },\n                    onpause: ()=>{\n                        updatePlayState({\n                            isPlaying: false,\n                            isPaused: true\n                        });\n                        clearProgressInterval();\n                    },\n                    onstop: ()=>{\n                        updatePlayState({\n                            isPlaying: false,\n                            isPaused: false,\n                            currentTime: 0\n                        });\n                        clearProgressInterval();\n                    },\n                    onend: ()=>{\n                        updatePlayState({\n                            isPlaying: false,\n                            isPaused: false,\n                            currentTime: 0\n                        });\n                        clearProgressInterval();\n                    },\n                    onloaderror: (id, error)=>{\n                        console.error(\"音频加载失败:\", error);\n                        updatePlayState({\n                            isLoading: false,\n                            error: \"音频加载失败\"\n                        });\n                    },\n                    onplayerror: (id, error)=>{\n                        console.error(\"音频播放失败:\", error);\n                        updatePlayState({\n                            isPlaying: false,\n                            error: \"音频播放失败\"\n                        });\n                    }\n                });\n                updatePlayState({\n                    isLoading: true\n                });\n            }\n            // 播放音频\n            if (howlRef.current) {\n                if (playState.isPaused) {\n                    howlRef.current.play();\n                } else {\n                    howlRef.current.play();\n                }\n            }\n        } catch (error) {\n            console.error(\"播放音频时发生错误:\", error);\n            updatePlayState({\n                error: \"播放失败\",\n                isLoading: false\n            });\n        }\n    }, [\n        currentSound,\n        playState.isLooping,\n        playState.isPaused,\n        userVolume,\n        setCurrentSound,\n        updatePlayState,\n        startProgressInterval\n    ]);\n    // 暂停音频\n    const pause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (howlRef.current && howlRef.current.playing()) {\n            howlRef.current.pause();\n        }\n    }, []);\n    // 停止音频\n    const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (howlRef.current) {\n            howlRef.current.stop();\n            howlRef.current.unload();\n            howlRef.current = null;\n        }\n        clearProgressInterval();\n        updatePlayState({\n            isPlaying: false,\n            isPaused: false,\n            currentTime: 0\n        });\n    }, [\n        clearProgressInterval,\n        updatePlayState\n    ]);\n    // 设置音量\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((volume)=>{\n        const clampedVolume = Math.max(0, Math.min(1, volume));\n        setUserVolume(clampedVolume);\n        updatePlayState({\n            volume: clampedVolume\n        });\n        if (howlRef.current) {\n            howlRef.current.volume(clampedVolume);\n        }\n        // 设置全局音量\n        howler__WEBPACK_IMPORTED_MODULE_1__.Howler.volume(clampedVolume);\n    }, [\n        setUserVolume,\n        updatePlayState\n    ]);\n    // 设置循环播放\n    const setLoop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((loop)=>{\n        updatePlayState({\n            isLooping: loop\n        });\n        if (howlRef.current) {\n            howlRef.current.loop(loop);\n        }\n    }, [\n        updatePlayState\n    ]);\n    // 跳转到指定位置\n    const seek = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((position)=>{\n        if (howlRef.current) {\n            howlRef.current.seek(position);\n            updatePlayState({\n                currentTime: position\n            });\n        }\n    }, [\n        updatePlayState\n    ]);\n    // 组件卸载时清理资源\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            stop();\n        };\n    }, [\n        stop\n    ]);\n    // 监听音量变化\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (howlRef.current && playState.volume !== userVolume) {\n            howlRef.current.volume(userVolume);\n        }\n    }, [\n        userVolume,\n        playState.volume\n    ]);\n    return {\n        play,\n        pause,\n        stop,\n        setVolume,\n        setLoop,\n        seek,\n        isPlaying: playState.isPlaying,\n        isPaused: playState.isPaused,\n        isLoading: playState.isLoading,\n        currentTime: playState.currentTime,\n        duration: playState.duration,\n        volume: playState.volume,\n        isLooping: playState.isLooping,\n        error: playState.error || null\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlQXVkaW9QbGF5ZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXVEO0FBQ2pCO0FBQ2E7QUFvQjVDLE1BQU1NLGlCQUFpQjtJQUM1QixNQUFNQyxVQUFVTiw2Q0FBTUEsQ0FBYztJQUNwQyxNQUFNTyxjQUFjUCw2Q0FBTUEsQ0FBd0I7SUFFbEQsTUFBTSxFQUNKUSxZQUFZLEVBQ1pDLFNBQVMsRUFDVEMsVUFBVSxFQUNWQyxlQUFlLEVBQ2ZDLGVBQWUsRUFDZkMsYUFBYSxFQUNkLEdBQUdULGdFQUFhQTtJQUVqQixRQUFRO0lBQ1IsTUFBTVUsd0JBQXdCYixrREFBV0EsQ0FBQztRQUN4QyxJQUFJTSxZQUFZUSxPQUFPLEVBQUU7WUFDdkJDLGNBQWNULFlBQVlRLE9BQU87WUFDakNSLFlBQVlRLE9BQU8sR0FBRztRQUN4QjtJQUNGLEdBQUcsRUFBRTtJQUVMLFlBQVk7SUFDWixNQUFNRSx3QkFBd0JoQixrREFBV0EsQ0FBQztRQUN4Q2E7UUFDQVAsWUFBWVEsT0FBTyxHQUFHRyxZQUFZO1lBQ2hDLElBQUlaLFFBQVFTLE9BQU8sSUFBSVQsUUFBUVMsT0FBTyxDQUFDSSxPQUFPLElBQUk7Z0JBQ2hELE1BQU1DLGNBQWNkLFFBQVFTLE9BQU8sQ0FBQ00sSUFBSTtnQkFDeENULGdCQUFnQjtvQkFBRVE7Z0JBQVk7WUFDaEM7UUFDRixHQUFHO0lBQ0wsR0FBRztRQUFDUjtRQUFpQkU7S0FBc0I7SUFFM0MsT0FBTztJQUNQLE1BQU1RLE9BQU9yQixrREFBV0EsQ0FBQyxDQUFDc0I7UUFDeEIsSUFBSTtZQUNGLG9CQUFvQjtZQUNwQixJQUFJQSxTQUFTQSxNQUFNQyxFQUFFLEtBQUtoQixjQUFjZ0IsSUFBSTtnQkFDMUNDO2dCQUNBZCxnQkFBZ0JZO2dCQUVoQixlQUFlO2dCQUNmLE1BQU1HLFdBQVcsQ0FBQyxRQUFRLEVBQUVILE1BQU1JLFFBQVEsQ0FBQyxDQUFDLEVBQUVKLE1BQU1LLFFBQVEsQ0FBQyxDQUFDO2dCQUM5RHRCLFFBQVFTLE9BQU8sR0FBRyxJQUFJYix3Q0FBSUEsQ0FBQztvQkFDekIyQixLQUFLO3dCQUFDSDtxQkFBUztvQkFDZkksUUFBUXBCO29CQUNScUIsTUFBTXRCLFVBQVV1QixTQUFTO29CQUN6QkMsUUFBUTt3QkFDTnJCLGdCQUFnQjs0QkFDZHNCLFdBQVc7NEJBQ1hDLFVBQVU3QixRQUFRUyxPQUFPLEVBQUVvQixjQUFjOzRCQUN6Q0MsT0FBTzt3QkFDVDtvQkFDRjtvQkFDQUMsUUFBUTt3QkFDTnpCLGdCQUFnQjs0QkFDZDBCLFdBQVc7NEJBQ1hDLFVBQVU7NEJBQ1ZILE9BQU87d0JBQ1Q7d0JBQ0FuQjtvQkFDRjtvQkFDQXVCLFNBQVM7d0JBQ1A1QixnQkFBZ0I7NEJBQ2QwQixXQUFXOzRCQUNYQyxVQUFVO3dCQUNaO3dCQUNBekI7b0JBQ0Y7b0JBQ0EyQixRQUFRO3dCQUNON0IsZ0JBQWdCOzRCQUNkMEIsV0FBVzs0QkFDWEMsVUFBVTs0QkFDVm5CLGFBQWE7d0JBQ2Y7d0JBQ0FOO29CQUNGO29CQUNBNEIsT0FBTzt3QkFDTDlCLGdCQUFnQjs0QkFDZDBCLFdBQVc7NEJBQ1hDLFVBQVU7NEJBQ1ZuQixhQUFhO3dCQUNmO3dCQUNBTjtvQkFDRjtvQkFDQTZCLGFBQWEsQ0FBQ25CLElBQUlZO3dCQUNoQlEsUUFBUVIsS0FBSyxDQUFDLFdBQVdBO3dCQUN6QnhCLGdCQUFnQjs0QkFDZHNCLFdBQVc7NEJBQ1hFLE9BQU87d0JBQ1Q7b0JBQ0Y7b0JBQ0FTLGFBQWEsQ0FBQ3JCLElBQUlZO3dCQUNoQlEsUUFBUVIsS0FBSyxDQUFDLFdBQVdBO3dCQUN6QnhCLGdCQUFnQjs0QkFDZDBCLFdBQVc7NEJBQ1hGLE9BQU87d0JBQ1Q7b0JBQ0Y7Z0JBQ0Y7Z0JBRUF4QixnQkFBZ0I7b0JBQUVzQixXQUFXO2dCQUFLO1lBQ3BDO1lBRUEsT0FBTztZQUNQLElBQUk1QixRQUFRUyxPQUFPLEVBQUU7Z0JBQ25CLElBQUlOLFVBQVU4QixRQUFRLEVBQUU7b0JBQ3RCakMsUUFBUVMsT0FBTyxDQUFDTyxJQUFJO2dCQUN0QixPQUFPO29CQUNMaEIsUUFBUVMsT0FBTyxDQUFDTyxJQUFJO2dCQUN0QjtZQUNGO1FBQ0YsRUFBRSxPQUFPYyxPQUFPO1lBQ2RRLFFBQVFSLEtBQUssQ0FBQyxjQUFjQTtZQUM1QnhCLGdCQUFnQjtnQkFDZHdCLE9BQU87Z0JBQ1BGLFdBQVc7WUFDYjtRQUNGO0lBQ0YsR0FBRztRQUFDMUI7UUFBY0MsVUFBVXVCLFNBQVM7UUFBRXZCLFVBQVU4QixRQUFRO1FBQUU3QjtRQUFZQztRQUFpQkM7UUFBaUJLO0tBQXNCO0lBRS9ILE9BQU87SUFDUCxNQUFNNkIsUUFBUTdDLGtEQUFXQSxDQUFDO1FBQ3hCLElBQUlLLFFBQVFTLE9BQU8sSUFBSVQsUUFBUVMsT0FBTyxDQUFDSSxPQUFPLElBQUk7WUFDaERiLFFBQVFTLE9BQU8sQ0FBQytCLEtBQUs7UUFDdkI7SUFDRixHQUFHLEVBQUU7SUFFTCxPQUFPO0lBQ1AsTUFBTXJCLE9BQU94QixrREFBV0EsQ0FBQztRQUN2QixJQUFJSyxRQUFRUyxPQUFPLEVBQUU7WUFDbkJULFFBQVFTLE9BQU8sQ0FBQ1UsSUFBSTtZQUNwQm5CLFFBQVFTLE9BQU8sQ0FBQ2dDLE1BQU07WUFDdEJ6QyxRQUFRUyxPQUFPLEdBQUc7UUFDcEI7UUFDQUQ7UUFDQUYsZ0JBQWdCO1lBQ2QwQixXQUFXO1lBQ1hDLFVBQVU7WUFDVm5CLGFBQWE7UUFDZjtJQUNGLEdBQUc7UUFBQ047UUFBdUJGO0tBQWdCO0lBRTNDLE9BQU87SUFDUCxNQUFNb0MsWUFBWS9DLGtEQUFXQSxDQUFDLENBQUM2QjtRQUM3QixNQUFNbUIsZ0JBQWdCQyxLQUFLQyxHQUFHLENBQUMsR0FBR0QsS0FBS0UsR0FBRyxDQUFDLEdBQUd0QjtRQUM5Q2pCLGNBQWNvQztRQUNkckMsZ0JBQWdCO1lBQUVrQixRQUFRbUI7UUFBYztRQUV4QyxJQUFJM0MsUUFBUVMsT0FBTyxFQUFFO1lBQ25CVCxRQUFRUyxPQUFPLENBQUNlLE1BQU0sQ0FBQ21CO1FBQ3pCO1FBRUEsU0FBUztRQUNUOUMsMENBQU1BLENBQUMyQixNQUFNLENBQUNtQjtJQUNoQixHQUFHO1FBQUNwQztRQUFlRDtLQUFnQjtJQUVuQyxTQUFTO0lBQ1QsTUFBTXlDLFVBQVVwRCxrREFBV0EsQ0FBQyxDQUFDOEI7UUFDM0JuQixnQkFBZ0I7WUFBRW9CLFdBQVdEO1FBQUs7UUFDbEMsSUFBSXpCLFFBQVFTLE9BQU8sRUFBRTtZQUNuQlQsUUFBUVMsT0FBTyxDQUFDZ0IsSUFBSSxDQUFDQTtRQUN2QjtJQUNGLEdBQUc7UUFBQ25CO0tBQWdCO0lBRXBCLFVBQVU7SUFDVixNQUFNUyxPQUFPcEIsa0RBQVdBLENBQUMsQ0FBQ3FEO1FBQ3hCLElBQUloRCxRQUFRUyxPQUFPLEVBQUU7WUFDbkJULFFBQVFTLE9BQU8sQ0FBQ00sSUFBSSxDQUFDaUM7WUFDckIxQyxnQkFBZ0I7Z0JBQUVRLGFBQWFrQztZQUFTO1FBQzFDO0lBQ0YsR0FBRztRQUFDMUM7S0FBZ0I7SUFFcEIsWUFBWTtJQUNaYixnREFBU0EsQ0FBQztRQUNSLE9BQU87WUFDTDBCO1FBQ0Y7SUFDRixHQUFHO1FBQUNBO0tBQUs7SUFFVCxTQUFTO0lBQ1QxQixnREFBU0EsQ0FBQztRQUNSLElBQUlPLFFBQVFTLE9BQU8sSUFBSU4sVUFBVXFCLE1BQU0sS0FBS3BCLFlBQVk7WUFDdERKLFFBQVFTLE9BQU8sQ0FBQ2UsTUFBTSxDQUFDcEI7UUFDekI7SUFDRixHQUFHO1FBQUNBO1FBQVlELFVBQVVxQixNQUFNO0tBQUM7SUFFakMsT0FBTztRQUNMUjtRQUNBd0I7UUFDQXJCO1FBQ0F1QjtRQUNBSztRQUNBaEM7UUFDQWlCLFdBQVc3QixVQUFVNkIsU0FBUztRQUM5QkMsVUFBVTlCLFVBQVU4QixRQUFRO1FBQzVCTCxXQUFXekIsVUFBVXlCLFNBQVM7UUFDOUJkLGFBQWFYLFVBQVVXLFdBQVc7UUFDbENlLFVBQVUxQixVQUFVMEIsUUFBUTtRQUM1QkwsUUFBUXJCLFVBQVVxQixNQUFNO1FBQ3hCRSxXQUFXdkIsVUFBVXVCLFNBQVM7UUFDOUJJLE9BQU8zQixVQUFVMkIsS0FBSyxJQUFJO0lBQzVCO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2hvb2tzL3VzZUF1ZGlvUGxheWVyLnRzPzY5NTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgSG93bCwgSG93bGVyIH0gZnJvbSAnaG93bGVyJztcbmltcG9ydCB7IHVzZUF1ZGlvU3RvcmUgfSBmcm9tICdAL3N0b3JlL2F1ZGlvU3RvcmUnO1xuaW1wb3J0IHsgTXVsdGlsaW5ndWFsQXVkaW9JdGVtIH0gZnJvbSAnQC90eXBlcy9hdWRpbyc7XG5cbmludGVyZmFjZSBVc2VBdWRpb1BsYXllclJldHVybiB7XG4gIHBsYXk6IChzb3VuZD86IE11bHRpbGluZ3VhbEF1ZGlvSXRlbSkgPT4gdm9pZDtcbiAgcGF1c2U6ICgpID0+IHZvaWQ7XG4gIHN0b3A6ICgpID0+IHZvaWQ7XG4gIHNldFZvbHVtZTogKHZvbHVtZTogbnVtYmVyKSA9PiB2b2lkO1xuICBzZXRMb29wOiAobG9vcDogYm9vbGVhbikgPT4gdm9pZDtcbiAgc2VlazogKHBvc2l0aW9uOiBudW1iZXIpID0+IHZvaWQ7XG4gIGlzUGxheWluZzogYm9vbGVhbjtcbiAgaXNQYXVzZWQ6IGJvb2xlYW47XG4gIGlzTG9hZGluZzogYm9vbGVhbjtcbiAgY3VycmVudFRpbWU6IG51bWJlcjtcbiAgZHVyYXRpb246IG51bWJlcjtcbiAgdm9sdW1lOiBudW1iZXI7XG4gIGlzTG9vcGluZzogYm9vbGVhbjtcbiAgZXJyb3I6IHN0cmluZyB8IG51bGw7XG59XG5cbmV4cG9ydCBjb25zdCB1c2VBdWRpb1BsYXllciA9ICgpOiBVc2VBdWRpb1BsYXllclJldHVybiA9PiB7XG4gIGNvbnN0IGhvd2xSZWYgPSB1c2VSZWY8SG93bCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBpbnRlcnZhbFJlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpO1xuICBcbiAgY29uc3Qge1xuICAgIGN1cnJlbnRTb3VuZCxcbiAgICBwbGF5U3RhdGUsXG4gICAgdXNlclZvbHVtZSxcbiAgICBzZXRDdXJyZW50U291bmQsXG4gICAgdXBkYXRlUGxheVN0YXRlLFxuICAgIHNldFVzZXJWb2x1bWUsXG4gIH0gPSB1c2VBdWRpb1N0b3JlKCk7XG5cbiAgLy8g5riF55CG5a6a5pe25ZmoXG4gIGNvbnN0IGNsZWFyUHJvZ3Jlc3NJbnRlcnZhbCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoaW50ZXJ2YWxSZWYuY3VycmVudCkge1xuICAgICAgY2xlYXJJbnRlcnZhbChpbnRlcnZhbFJlZi5jdXJyZW50KTtcbiAgICAgIGludGVydmFsUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIC8vIOW8gOWni+i/m+W6puabtOaWsOWumuaXtuWZqFxuICBjb25zdCBzdGFydFByb2dyZXNzSW50ZXJ2YWwgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgY2xlYXJQcm9ncmVzc0ludGVydmFsKCk7XG4gICAgaW50ZXJ2YWxSZWYuY3VycmVudCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIGlmIChob3dsUmVmLmN1cnJlbnQgJiYgaG93bFJlZi5jdXJyZW50LnBsYXlpbmcoKSkge1xuICAgICAgICBjb25zdCBjdXJyZW50VGltZSA9IGhvd2xSZWYuY3VycmVudC5zZWVrKCkgYXMgbnVtYmVyO1xuICAgICAgICB1cGRhdGVQbGF5U3RhdGUoeyBjdXJyZW50VGltZSB9KTtcbiAgICAgIH1cbiAgICB9LCAxMDAwKTtcbiAgfSwgW3VwZGF0ZVBsYXlTdGF0ZSwgY2xlYXJQcm9ncmVzc0ludGVydmFsXSk7XG5cbiAgLy8g5pKt5pS+6Z+z6aKRXG4gIGNvbnN0IHBsYXkgPSB1c2VDYWxsYmFjaygoc291bmQ/OiBNdWx0aWxpbmd1YWxBdWRpb0l0ZW0pID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8g5aaC5p6c5Lyg5YWl5LqG5paw55qE6Z+z6aKR77yM5YWI5YGc5q2i5b2T5YmN5pKt5pS+XG4gICAgICBpZiAoc291bmQgJiYgc291bmQuaWQgIT09IGN1cnJlbnRTb3VuZD8uaWQpIHtcbiAgICAgICAgc3RvcCgpO1xuICAgICAgICBzZXRDdXJyZW50U291bmQoc291bmQpO1xuICAgICAgICBcbiAgICAgICAgLy8g5Yib5bu65paw55qEIEhvd2wg5a6e5L6LXG4gICAgICAgIGNvbnN0IGF1ZGlvVXJsID0gYC9Tb3VuZHMvJHtzb3VuZC5jYXRlZ29yeX0vJHtzb3VuZC5maWxlbmFtZX1gO1xuICAgICAgICBob3dsUmVmLmN1cnJlbnQgPSBuZXcgSG93bCh7XG4gICAgICAgICAgc3JjOiBbYXVkaW9VcmxdLFxuICAgICAgICAgIHZvbHVtZTogdXNlclZvbHVtZSxcbiAgICAgICAgICBsb29wOiBwbGF5U3RhdGUuaXNMb29waW5nLFxuICAgICAgICAgIG9ubG9hZDogKCkgPT4ge1xuICAgICAgICAgICAgdXBkYXRlUGxheVN0YXRlKHtcbiAgICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICAgICAgZHVyYXRpb246IGhvd2xSZWYuY3VycmVudD8uZHVyYXRpb24oKSB8fCAwLFxuICAgICAgICAgICAgICBlcnJvcjogbnVsbCxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgb25wbGF5OiAoKSA9PiB7XG4gICAgICAgICAgICB1cGRhdGVQbGF5U3RhdGUoe1xuICAgICAgICAgICAgICBpc1BsYXlpbmc6IHRydWUsXG4gICAgICAgICAgICAgIGlzUGF1c2VkOiBmYWxzZSxcbiAgICAgICAgICAgICAgZXJyb3I6IG51bGwsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHN0YXJ0UHJvZ3Jlc3NJbnRlcnZhbCgpO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgb25wYXVzZTogKCkgPT4ge1xuICAgICAgICAgICAgdXBkYXRlUGxheVN0YXRlKHtcbiAgICAgICAgICAgICAgaXNQbGF5aW5nOiBmYWxzZSxcbiAgICAgICAgICAgICAgaXNQYXVzZWQ6IHRydWUsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNsZWFyUHJvZ3Jlc3NJbnRlcnZhbCgpO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgb25zdG9wOiAoKSA9PiB7XG4gICAgICAgICAgICB1cGRhdGVQbGF5U3RhdGUoe1xuICAgICAgICAgICAgICBpc1BsYXlpbmc6IGZhbHNlLFxuICAgICAgICAgICAgICBpc1BhdXNlZDogZmFsc2UsXG4gICAgICAgICAgICAgIGN1cnJlbnRUaW1lOiAwLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjbGVhclByb2dyZXNzSW50ZXJ2YWwoKTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIG9uZW5kOiAoKSA9PiB7XG4gICAgICAgICAgICB1cGRhdGVQbGF5U3RhdGUoe1xuICAgICAgICAgICAgICBpc1BsYXlpbmc6IGZhbHNlLFxuICAgICAgICAgICAgICBpc1BhdXNlZDogZmFsc2UsXG4gICAgICAgICAgICAgIGN1cnJlbnRUaW1lOiAwLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjbGVhclByb2dyZXNzSW50ZXJ2YWwoKTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIG9ubG9hZGVycm9yOiAoaWQsIGVycm9yKSA9PiB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfpn7PpopHliqDovb3lpLHotKU6JywgZXJyb3IpO1xuICAgICAgICAgICAgdXBkYXRlUGxheVN0YXRlKHtcbiAgICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICAgICAgZXJyb3I6ICfpn7PpopHliqDovb3lpLHotKUnLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBvbnBsYXllcnJvcjogKGlkLCBlcnJvcikgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign6Z+z6aKR5pKt5pS+5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgICAgIHVwZGF0ZVBsYXlTdGF0ZSh7XG4gICAgICAgICAgICAgIGlzUGxheWluZzogZmFsc2UsXG4gICAgICAgICAgICAgIGVycm9yOiAn6Z+z6aKR5pKt5pS+5aSx6LSlJyxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuXG4gICAgICAgIHVwZGF0ZVBsYXlTdGF0ZSh7IGlzTG9hZGluZzogdHJ1ZSB9KTtcbiAgICAgIH1cblxuICAgICAgLy8g5pKt5pS+6Z+z6aKRXG4gICAgICBpZiAoaG93bFJlZi5jdXJyZW50KSB7XG4gICAgICAgIGlmIChwbGF5U3RhdGUuaXNQYXVzZWQpIHtcbiAgICAgICAgICBob3dsUmVmLmN1cnJlbnQucGxheSgpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGhvd2xSZWYuY3VycmVudC5wbGF5KCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign5pKt5pS+6Z+z6aKR5pe25Y+R55Sf6ZSZ6K+vOicsIGVycm9yKTtcbiAgICAgIHVwZGF0ZVBsYXlTdGF0ZSh7XG4gICAgICAgIGVycm9yOiAn5pKt5pS+5aSx6LSlJyxcbiAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW2N1cnJlbnRTb3VuZCwgcGxheVN0YXRlLmlzTG9vcGluZywgcGxheVN0YXRlLmlzUGF1c2VkLCB1c2VyVm9sdW1lLCBzZXRDdXJyZW50U291bmQsIHVwZGF0ZVBsYXlTdGF0ZSwgc3RhcnRQcm9ncmVzc0ludGVydmFsXSk7XG5cbiAgLy8g5pqC5YGc6Z+z6aKRXG4gIGNvbnN0IHBhdXNlID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmIChob3dsUmVmLmN1cnJlbnQgJiYgaG93bFJlZi5jdXJyZW50LnBsYXlpbmcoKSkge1xuICAgICAgaG93bFJlZi5jdXJyZW50LnBhdXNlKCk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8g5YGc5q2i6Z+z6aKRXG4gIGNvbnN0IHN0b3AgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKGhvd2xSZWYuY3VycmVudCkge1xuICAgICAgaG93bFJlZi5jdXJyZW50LnN0b3AoKTtcbiAgICAgIGhvd2xSZWYuY3VycmVudC51bmxvYWQoKTtcbiAgICAgIGhvd2xSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuICAgIGNsZWFyUHJvZ3Jlc3NJbnRlcnZhbCgpO1xuICAgIHVwZGF0ZVBsYXlTdGF0ZSh7XG4gICAgICBpc1BsYXlpbmc6IGZhbHNlLFxuICAgICAgaXNQYXVzZWQ6IGZhbHNlLFxuICAgICAgY3VycmVudFRpbWU6IDAsXG4gICAgfSk7XG4gIH0sIFtjbGVhclByb2dyZXNzSW50ZXJ2YWwsIHVwZGF0ZVBsYXlTdGF0ZV0pO1xuXG4gIC8vIOiuvue9rumfs+mHj1xuICBjb25zdCBzZXRWb2x1bWUgPSB1c2VDYWxsYmFjaygodm9sdW1lOiBudW1iZXIpID0+IHtcbiAgICBjb25zdCBjbGFtcGVkVm9sdW1lID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oMSwgdm9sdW1lKSk7XG4gICAgc2V0VXNlclZvbHVtZShjbGFtcGVkVm9sdW1lKTtcbiAgICB1cGRhdGVQbGF5U3RhdGUoeyB2b2x1bWU6IGNsYW1wZWRWb2x1bWUgfSk7XG4gICAgXG4gICAgaWYgKGhvd2xSZWYuY3VycmVudCkge1xuICAgICAgaG93bFJlZi5jdXJyZW50LnZvbHVtZShjbGFtcGVkVm9sdW1lKTtcbiAgICB9XG4gICAgXG4gICAgLy8g6K6+572u5YWo5bGA6Z+z6YePXG4gICAgSG93bGVyLnZvbHVtZShjbGFtcGVkVm9sdW1lKTtcbiAgfSwgW3NldFVzZXJWb2x1bWUsIHVwZGF0ZVBsYXlTdGF0ZV0pO1xuXG4gIC8vIOiuvue9ruW+queOr+aSreaUvlxuICBjb25zdCBzZXRMb29wID0gdXNlQ2FsbGJhY2soKGxvb3A6IGJvb2xlYW4pID0+IHtcbiAgICB1cGRhdGVQbGF5U3RhdGUoeyBpc0xvb3Bpbmc6IGxvb3AgfSk7XG4gICAgaWYgKGhvd2xSZWYuY3VycmVudCkge1xuICAgICAgaG93bFJlZi5jdXJyZW50Lmxvb3AobG9vcCk7XG4gICAgfVxuICB9LCBbdXBkYXRlUGxheVN0YXRlXSk7XG5cbiAgLy8g6Lez6L2s5Yiw5oyH5a6a5L2N572uXG4gIGNvbnN0IHNlZWsgPSB1c2VDYWxsYmFjaygocG9zaXRpb246IG51bWJlcikgPT4ge1xuICAgIGlmIChob3dsUmVmLmN1cnJlbnQpIHtcbiAgICAgIGhvd2xSZWYuY3VycmVudC5zZWVrKHBvc2l0aW9uKTtcbiAgICAgIHVwZGF0ZVBsYXlTdGF0ZSh7IGN1cnJlbnRUaW1lOiBwb3NpdGlvbiB9KTtcbiAgICB9XG4gIH0sIFt1cGRhdGVQbGF5U3RhdGVdKTtcblxuICAvLyDnu4Tku7bljbjovb3ml7bmuIXnkIbotYTmupBcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgc3RvcCgpO1xuICAgIH07XG4gIH0sIFtzdG9wXSk7XG5cbiAgLy8g55uR5ZCs6Z+z6YeP5Y+Y5YyWXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGhvd2xSZWYuY3VycmVudCAmJiBwbGF5U3RhdGUudm9sdW1lICE9PSB1c2VyVm9sdW1lKSB7XG4gICAgICBob3dsUmVmLmN1cnJlbnQudm9sdW1lKHVzZXJWb2x1bWUpO1xuICAgIH1cbiAgfSwgW3VzZXJWb2x1bWUsIHBsYXlTdGF0ZS52b2x1bWVdKTtcblxuICByZXR1cm4ge1xuICAgIHBsYXksXG4gICAgcGF1c2UsXG4gICAgc3RvcCxcbiAgICBzZXRWb2x1bWUsXG4gICAgc2V0TG9vcCxcbiAgICBzZWVrLFxuICAgIGlzUGxheWluZzogcGxheVN0YXRlLmlzUGxheWluZyxcbiAgICBpc1BhdXNlZDogcGxheVN0YXRlLmlzUGF1c2VkLFxuICAgIGlzTG9hZGluZzogcGxheVN0YXRlLmlzTG9hZGluZyxcbiAgICBjdXJyZW50VGltZTogcGxheVN0YXRlLmN1cnJlbnRUaW1lLFxuICAgIGR1cmF0aW9uOiBwbGF5U3RhdGUuZHVyYXRpb24sXG4gICAgdm9sdW1lOiBwbGF5U3RhdGUudm9sdW1lLFxuICAgIGlzTG9vcGluZzogcGxheVN0YXRlLmlzTG9vcGluZyxcbiAgICBlcnJvcjogcGxheVN0YXRlLmVycm9yIHx8IG51bGwsXG4gIH07XG59O1xuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZUNhbGxiYWNrIiwiSG93bCIsIkhvd2xlciIsInVzZUF1ZGlvU3RvcmUiLCJ1c2VBdWRpb1BsYXllciIsImhvd2xSZWYiLCJpbnRlcnZhbFJlZiIsImN1cnJlbnRTb3VuZCIsInBsYXlTdGF0ZSIsInVzZXJWb2x1bWUiLCJzZXRDdXJyZW50U291bmQiLCJ1cGRhdGVQbGF5U3RhdGUiLCJzZXRVc2VyVm9sdW1lIiwiY2xlYXJQcm9ncmVzc0ludGVydmFsIiwiY3VycmVudCIsImNsZWFySW50ZXJ2YWwiLCJzdGFydFByb2dyZXNzSW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsInBsYXlpbmciLCJjdXJyZW50VGltZSIsInNlZWsiLCJwbGF5Iiwic291bmQiLCJpZCIsInN0b3AiLCJhdWRpb1VybCIsImNhdGVnb3J5IiwiZmlsZW5hbWUiLCJzcmMiLCJ2b2x1bWUiLCJsb29wIiwiaXNMb29waW5nIiwib25sb2FkIiwiaXNMb2FkaW5nIiwiZHVyYXRpb24iLCJlcnJvciIsIm9ucGxheSIsImlzUGxheWluZyIsImlzUGF1c2VkIiwib25wYXVzZSIsIm9uc3RvcCIsIm9uZW5kIiwib25sb2FkZXJyb3IiLCJjb25zb2xlIiwib25wbGF5ZXJyb3IiLCJwYXVzZSIsInVubG9hZCIsInNldFZvbHVtZSIsImNsYW1wZWRWb2x1bWUiLCJNYXRoIiwibWF4IiwibWluIiwic2V0TG9vcCIsInBvc2l0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAudioPlayer.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/audioStore.ts":
/*!*********************************!*\
  !*** ./src/store/audioStore.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioStore: () => (/* binding */ useAudioStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useAudioStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // 初始状态\n        currentSound: null,\n        playState: {\n            isPlaying: false,\n            isPaused: false,\n            isLoading: false,\n            currentTime: 0,\n            duration: 0,\n            volume: 0.7,\n            isLooping: false\n        },\n        // 播放器UI初始状态\n        playerUI: {\n            mode: \"standard\",\n            isVisible: false,\n            position: \"bottom\",\n            isMinimized: false,\n            showTimerPanel: false,\n            showMixingPanel: false\n        },\n        mixingChannels: [],\n        maxChannels: 2,\n        masterVolume: 0.8,\n        timer: {\n            duration: 0,\n            isActive: false,\n            remainingTime: 0,\n            fadeOutDuration: 10,\n            autoStop: true\n        },\n        favorites: [],\n        recentlyPlayed: [],\n        userVolume: 0.7,\n        // 播放控制方法\n        setCurrentSound: (sound)=>{\n            set({\n                currentSound: sound\n            });\n            if (sound) {\n                get().addToRecentlyPlayed(sound.id);\n                // 自动显示播放器\n                get().setPlayerVisible(true);\n            }\n        },\n        updatePlayState: (newState)=>{\n            set((state)=>({\n                    playState: {\n                        ...state.playState,\n                        ...newState\n                    }\n                }));\n        },\n        // 混音控制方法\n        addMixingChannel: (sound)=>{\n            const { mixingChannels, maxChannels } = get();\n            if (mixingChannels.length >= maxChannels) {\n                console.warn(`MVP版本最多支持${maxChannels}个音频同时播放`);\n                return false;\n            }\n            const newChannel = {\n                id: `channel_${Date.now()}`,\n                soundId: sound.id,\n                volume: 0.7,\n                isMuted: false,\n                isActive: true\n            };\n            set((state)=>({\n                    mixingChannels: [\n                        ...state.mixingChannels,\n                        newChannel\n                    ]\n                }));\n            return true;\n        },\n        removeMixingChannel: (channelId)=>{\n            set((state)=>({\n                    mixingChannels: state.mixingChannels.filter((channel)=>channel.id !== channelId)\n                }));\n        },\n        updateChannelVolume: (channelId, volume)=>{\n            set((state)=>({\n                    mixingChannels: state.mixingChannels.map((channel)=>channel.id === channelId ? {\n                            ...channel,\n                            volume\n                        } : channel)\n                }));\n        },\n        setMasterVolume: (volume)=>{\n            set({\n                masterVolume: volume\n            });\n        },\n        // 定时器方法\n        setTimer: (duration)=>{\n            set({\n                timer: {\n                    duration,\n                    isActive: true,\n                    remainingTime: duration * 60,\n                    fadeOutDuration: 10,\n                    autoStop: true\n                }\n            });\n        },\n        clearTimer: ()=>{\n            set((state)=>({\n                    timer: {\n                        ...state.timer,\n                        isActive: false,\n                        remainingTime: 0\n                    }\n                }));\n        },\n        updateTimerRemaining: (remaining)=>{\n            set((state)=>({\n                    timer: {\n                        ...state.timer,\n                        remainingTime: remaining\n                    }\n                }));\n        },\n        // 用户偏好方法\n        addToFavorites: (soundId)=>{\n            set((state)=>({\n                    favorites: state.favorites.includes(soundId) ? state.favorites : [\n                        ...state.favorites,\n                        soundId\n                    ]\n                }));\n        },\n        removeFromFavorites: (soundId)=>{\n            set((state)=>({\n                    favorites: state.favorites.filter((id)=>id !== soundId)\n                }));\n        },\n        addToRecentlyPlayed: (soundId)=>{\n            set((state)=>{\n                const filtered = state.recentlyPlayed.filter((id)=>id !== soundId);\n                return {\n                    recentlyPlayed: [\n                        soundId,\n                        ...filtered\n                    ].slice(0, 20) // 保留最近20个\n                };\n            });\n        },\n        setUserVolume: (volume)=>{\n            set({\n                userVolume: volume\n            });\n        },\n        // 播放器UI控制方法实现\n        setPlayerMode: (mode)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        mode\n                    }\n                }));\n        },\n        setPlayerVisible: (visible)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        isVisible: visible\n                    }\n                }));\n        },\n        setPlayerPosition: (position)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        position\n                    }\n                }));\n        },\n        togglePlayerMinimized: ()=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        isMinimized: !state.playerUI.isMinimized\n                    }\n                }));\n        },\n        setTimerPanelVisible: (visible)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        showTimerPanel: visible\n                    }\n                }));\n        },\n        setMixingPanelVisible: (visible)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        showMixingPanel: visible\n                    }\n                }));\n        }\n    }), {\n    name: \"noisesleep-audio-store\",\n    partialize: (state)=>({\n            favorites: state.favorites,\n            recentlyPlayed: state.recentlyPlayed,\n            userVolume: state.userVolume,\n            masterVolume: state.masterVolume,\n            playerUI: state.playerUI\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/audioStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"95df7b7bb871\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm9pc2VzbGVlcC13ZWIvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2JlNTUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5NWRmN2I3YmI4NzFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/[locale]/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/[locale]/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/[locale]/layout.tsx\",\"import\":\"Noto_Sans_SC\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-noto-sans-sc\",\"display\":\"swap\"}],\"variableName\":\"notoSansSC\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/[locale]/layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_SC\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-noto-sans-sc\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansSC\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n/routing */ \"(rsc)/./src/i18n/routing.ts\");\n/* harmony import */ var _components_AudioPlayer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AudioPlayer */ \"(rsc)/./src/components/AudioPlayer/index.ts\");\n\n\n\n\n\n\n\n\nfunction generateStaticParams() {\n    return _i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.map((locale)=>({\n            locale\n        }));\n}\nasync function generateMetadata({ params: { locale } }) {\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const meta = messages.meta;\n    return {\n        title: meta.title,\n        description: meta.description,\n        keywords: meta.keywords,\n        openGraph: {\n            title: meta.title,\n            description: meta.description,\n            url: `https://noisesleep.com${locale === \"en\" ? \"\" : \"/zh\"}`,\n            siteName: \"NoiseSleep\",\n            locale: locale === \"zh\" ? \"zh_CN\" : \"en_US\",\n            type: \"website\"\n        },\n        twitter: {\n            card: \"summary_large_image\",\n            title: meta.title,\n            description: meta.description\n        },\n        alternates: {\n            canonical: `https://noisesleep.com${locale === \"en\" ? \"\" : \"/zh\"}`,\n            languages: {\n                \"en\": \"https://noisesleep.com\",\n                \"zh\": \"https://noisesleep.com/zh\"\n            }\n        }\n    };\n}\nasync function LocaleLayout({ children, params: { locale } }) {\n    // 验证locale是否有效\n    if (!_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    // 获取翻译消息\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        dir: \"ltr\",\n        className: `${(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_6___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"en\",\n                        href: \"https://noisesleep.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"zh\",\n                        href: \"https://noisesleep.com/zh\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"x-default\",\n                        href: \"https://noisesleep.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#f59e0b\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://cdn.noisesleep.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"Content-Security-Policy\",\n                        content: \" default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; media-src 'self' blob: https://cdn.noisesleep.com; connect-src 'self' https://www.google-analytics.com; \"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `\n          ${locale === \"zh\" ? \"font-noto-sans-sc\" : \"font-inter\"}\n          antialiased\n          bg-white dark:bg-gray-900\n          text-gray-900 dark:text-gray-100\n          transition-colors duration-300\n        `,\n                \"data-locale\": locale,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioPlayer__WEBPACK_IMPORTED_MODULE_3__.AudioPlayerProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/test-player/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/[locale]/test-player/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/test-player/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUdNQTtBQUZpQjtBQUlSLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztrQkFDQyw0RUFBQ0M7WUFBS0MsV0FBV0wsK0pBQWU7c0JBQzdCRTs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJztcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbD5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/AudioPlayer.tsx":
/*!****************************************************!*\
  !*** ./src/components/AudioPlayer/AudioPlayer.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AudioPlayer: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx#AudioPlayer`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/AudioPlayerProvider.tsx":
/*!************************************************************!*\
  !*** ./src/components/AudioPlayer/AudioPlayerProvider.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AudioPlayerProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx#AudioPlayerProvider`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/PlayButton.tsx":
/*!***************************************************!*\
  !*** ./src/components/AudioPlayer/PlayButton.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GhostPlayButton: () => (/* binding */ e3),
/* harmony export */   LargePlayButton: () => (/* binding */ e4),
/* harmony export */   PlayButton: () => (/* binding */ e0),
/* harmony export */   PrimaryPlayButton: () => (/* binding */ e1),
/* harmony export */   SecondaryPlayButton: () => (/* binding */ e2),
/* harmony export */   SmallPlayButton: () => (/* binding */ e5)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#PlayButton`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#PrimaryPlayButton`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#SecondaryPlayButton`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#GhostPlayButton`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#LargePlayButton`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#SmallPlayButton`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/ProgressBar.tsx":
/*!****************************************************!*\
  !*** ./src/components/AudioPlayer/ProgressBar.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProgressBar: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx#ProgressBar`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/StandardPlayer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/AudioPlayer/StandardPlayer.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   StandardPlayer: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx#StandardPlayer`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/VolumeControl.tsx":
/*!******************************************************!*\
  !*** ./src/components/AudioPlayer/VolumeControl.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   VolumeControl: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx#VolumeControl`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/index.ts":
/*!*********************************************!*\
  !*** ./src/components/AudioPlayer/index.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioPlayer: () => (/* reexport safe */ _AudioPlayer__WEBPACK_IMPORTED_MODULE_0__.AudioPlayer),\n/* harmony export */   AudioPlayerProvider: () => (/* reexport safe */ _AudioPlayerProvider__WEBPACK_IMPORTED_MODULE_5__.AudioPlayerProvider),\n/* harmony export */   GhostPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.GhostPlayButton),\n/* harmony export */   LargePlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.LargePlayButton),\n/* harmony export */   PlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.PlayButton),\n/* harmony export */   PrimaryPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.PrimaryPlayButton),\n/* harmony export */   ProgressBar: () => (/* reexport safe */ _ProgressBar__WEBPACK_IMPORTED_MODULE_3__.ProgressBar),\n/* harmony export */   SecondaryPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.SecondaryPlayButton),\n/* harmony export */   SmallPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.SmallPlayButton),\n/* harmony export */   StandardPlayer: () => (/* reexport safe */ _StandardPlayer__WEBPACK_IMPORTED_MODULE_4__.StandardPlayer),\n/* harmony export */   VolumeControl: () => (/* reexport safe */ _VolumeControl__WEBPACK_IMPORTED_MODULE_2__.VolumeControl)\n/* harmony export */ });\n/* harmony import */ var _AudioPlayer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AudioPlayer */ \"(rsc)/./src/components/AudioPlayer/AudioPlayer.tsx\");\n/* harmony import */ var _PlayButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PlayButton */ \"(rsc)/./src/components/AudioPlayer/PlayButton.tsx\");\n/* harmony import */ var _VolumeControl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./VolumeControl */ \"(rsc)/./src/components/AudioPlayer/VolumeControl.tsx\");\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProgressBar */ \"(rsc)/./src/components/AudioPlayer/ProgressBar.tsx\");\n/* harmony import */ var _StandardPlayer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StandardPlayer */ \"(rsc)/./src/components/AudioPlayer/StandardPlayer.tsx\");\n/* harmony import */ var _AudioPlayerProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AudioPlayerProvider */ \"(rsc)/./src/components/AudioPlayer/AudioPlayerProvider.tsx\");\n// 主要组件\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9BdWRpb1BsYXllci9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLE9BQU87QUFDcUM7QUFDeUY7QUFDckY7QUFDSjtBQUNNO0FBQ1UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub2lzZXNsZWVwLXdlYi8uL3NyYy9jb21wb25lbnRzL0F1ZGlvUGxheWVyL2luZGV4LnRzPzZhYzgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8g5Li76KaB57uE5Lu2XG5leHBvcnQgeyBBdWRpb1BsYXllciB9IGZyb20gJy4vQXVkaW9QbGF5ZXInO1xuZXhwb3J0IHsgUGxheUJ1dHRvbiwgUHJpbWFyeVBsYXlCdXR0b24sIFNlY29uZGFyeVBsYXlCdXR0b24sIEdob3N0UGxheUJ1dHRvbiwgTGFyZ2VQbGF5QnV0dG9uLCBTbWFsbFBsYXlCdXR0b24gfSBmcm9tICcuL1BsYXlCdXR0b24nO1xuZXhwb3J0IHsgVm9sdW1lQ29udHJvbCB9IGZyb20gJy4vVm9sdW1lQ29udHJvbCc7XG5leHBvcnQgeyBQcm9ncmVzc0JhciB9IGZyb20gJy4vUHJvZ3Jlc3NCYXInO1xuZXhwb3J0IHsgU3RhbmRhcmRQbGF5ZXIgfSBmcm9tICcuL1N0YW5kYXJkUGxheWVyJztcbmV4cG9ydCB7IEF1ZGlvUGxheWVyUHJvdmlkZXIgfSBmcm9tICcuL0F1ZGlvUGxheWVyUHJvdmlkZXInO1xuXG4vLyDnsbvlnovlrprkuYlcbmV4cG9ydCB0eXBlIHsgZGVmYXVsdCBhcyBBdWRpb1BsYXllclByb3BzIH0gZnJvbSAnLi9BdWRpb1BsYXllcic7XG5leHBvcnQgdHlwZSB7IGRlZmF1bHQgYXMgUGxheUJ1dHRvblByb3BzIH0gZnJvbSAnLi9QbGF5QnV0dG9uJztcbmV4cG9ydCB0eXBlIHsgZGVmYXVsdCBhcyBWb2x1bWVDb250cm9sUHJvcHMgfSBmcm9tICcuL1ZvbHVtZUNvbnRyb2wnO1xuZXhwb3J0IHR5cGUgeyBkZWZhdWx0IGFzIFByb2dyZXNzQmFyUHJvcHMgfSBmcm9tICcuL1Byb2dyZXNzQmFyJztcbiJdLCJuYW1lcyI6WyJBdWRpb1BsYXllciIsIlBsYXlCdXR0b24iLCJQcmltYXJ5UGxheUJ1dHRvbiIsIlNlY29uZGFyeVBsYXlCdXR0b24iLCJHaG9zdFBsYXlCdXR0b24iLCJMYXJnZVBsYXlCdXR0b24iLCJTbWFsbFBsYXlCdXR0b24iLCJWb2x1bWVDb250cm9sIiwiUHJvZ3Jlc3NCYXIiLCJTdGFuZGFyZFBsYXllciIsIkF1ZGlvUGxheWVyUHJvdmlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/AudioPlayer/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/i18n/request.ts":
/*!*****************************!*\
  !*** ./src/i18n/request.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n/* harmony import */ var _routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./routing */ \"(rsc)/./src/i18n/routing.ts\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ locale })=>{\n    // 验证传入的locale是否有效\n    if (!_routing__WEBPACK_IMPORTED_MODULE_0__.routing.locales.includes(locale)) {\n        throw new Error(`Invalid locale: ${locale}`);\n    }\n    return {\n        messages: (await __webpack_require__(\"(rsc)/./src/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default,\n        timeZone: locale === \"zh\" ? \"Asia/Shanghai\" : \"America/New_York\",\n        now: new Date(),\n        formats: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                }\n            },\n            number: {\n                precise: {\n                    maximumFractionDigits: 2\n                }\n            }\n        }\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/request.ts\n");

/***/ }),

/***/ "(rsc)/./src/i18n/routing.ts":
/*!*****************************!*\
  !*** ./src/i18n/routing.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   routing: () => (/* binding */ routing)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/development/routing.js\");\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__.defineRouting)({\n    // 支持的语言列表\n    locales: [\n        \"en\",\n        \"zh\"\n    ],\n    // 默认语言\n    defaultLocale: \"en\",\n    // 语言前缀配置\n    localePrefix: {\n        mode: \"as-needed\",\n        prefixes: {\n            \"zh\": \"/zh\"\n        }\n    },\n    // 路径名配置\n    pathnames: {\n        \"/\": \"/\",\n        \"/about\": \"/about\",\n        \"/sounds\": \"/sounds\",\n        \"/sounds/[category]\": {\n            en: \"/sounds/[category]\",\n            zh: \"/sounds/[category]\"\n        },\n        \"/sounds/[category]/[sound]\": {\n            en: \"/sounds/[category]/[sound]\",\n            zh: \"/sounds/[category]/[sound]\"\n        },\n        \"/mix\": \"/mix\",\n        \"/favorites\": \"/favorites\",\n        \"/settings\": \"/settings\"\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/routing.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@formatjs","vendor-chunks/howler","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/zustand","vendor-chunks/next-intl","vendor-chunks/use-sync-external-store","vendor-chunks/@heroicons","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Ftest-player%2Fpage&page=%2F%5Blocale%5D%2Ftest-player%2Fpage&appPaths=%2F%5Blocale%5D%2Ftest-player%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Ftest-player%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();