/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/page";
exports.ids = ["app/[locale]/page"];
exports.modules = {

/***/ "(rsc)/./src/i18n/locales lazy recursive ^\\.\\/.*\\.json$":
/*!****************************************************************!*\
  !*** ./src/i18n/locales/ lazy ^\.\/.*\.json$ namespace object ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./src/i18n/locales/en.json",
		"_rsc_src_i18n_locales_en_json"
	],
	"./zh.json": [
		"(rsc)/./src/i18n/locales/zh.json",
		"_rsc_src_i18n_locales_zh_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./src/i18n/locales lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/page.tsx */ \"(rsc)/./src/app/[locale]/page.tsx\")), \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\")), \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/page\",\n        pathname: \"/[locale]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FCategoryCard%2FCategoryCard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FCTASection%2FCTASection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FHeroSection%2FHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FLayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FLayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FLayout%2FPageLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FCategoryCard%2FCategoryCard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FCTASection%2FCTASection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FHeroSection%2FHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FLayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FLayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FLayout%2FPageLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CategoryCard/CategoryCard.tsx */ \"(ssr)/./src/components/CategoryCard/CategoryCard.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CTASection/CTASection.tsx */ \"(ssr)/./src/components/CTASection/CTASection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/HeroSection/HeroSection.tsx */ \"(ssr)/./src/components/HeroSection/HeroSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layout/Footer.tsx */ \"(ssr)/./src/components/Layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layout/Header.tsx */ \"(ssr)/./src/components/Layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layout/PageLayout.tsx */ \"(ssr)/./src/components/Layout/PageLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FCategoryCard%2FCategoryCard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FCTASection%2FCTASection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FHeroSection%2FHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FLayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FLayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FLayout%2FPageLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/CTASection/CTASection.tsx":
/*!**************************************************!*\
  !*** ./src/components/CTASection/CTASection.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CTASection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CTASection({ className = \"\" }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"landing\");\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useLocale)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-indigo-600 to-purple-600 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-4xl font-light text-white mb-6\",\n                    children: t(\"sections.ctaTitle\")\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CTASection/CTASection.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl text-indigo-100 mb-8 max-w-2xl mx-auto\",\n                    children: t(\"sections.ctaDescription\")\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CTASection/CTASection.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: `/${locale}/sounds`,\n                    className: \"inline-block bg-white text-indigo-600 px-8 py-4 rounded-full text-lg font-medium hover:bg-gray-50 transition-all transform hover:scale-105\",\n                    children: t(\"buttons.startFree\")\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CTASection/CTASection.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CTASection/CTASection.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CTASection/CTASection.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CTASection/CTASection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CategoryCard/CategoryCard.tsx":
/*!******************************************************!*\
  !*** ./src/components/CategoryCard/CategoryCard.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CategoryCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CategoryCard({ icon, type, name, description, className = \"\" }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"landing\");\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getTypeColor = (type)=>{\n        const colors = {\n            noise: \"bg-blue-100 text-blue-800\",\n            things: \"bg-gray-100 text-gray-800\",\n            transport: \"bg-yellow-100 text-yellow-800\",\n            places: \"bg-purple-100 text-purple-800\",\n            urban: \"bg-orange-100 text-orange-800\",\n            animals: \"bg-green-100 text-green-800\",\n            rain: \"bg-cyan-100 text-cyan-800\",\n            nature: \"bg-emerald-100 text-emerald-800\"\n        };\n        return colors[type] || \"bg-gray-100 text-gray-800\";\n    };\n    const handlePlay = ()=>{\n        // 简单的演示功能，切换播放状态\n        setIsPlaying(!isPlaying);\n        // 3秒后自动停止（模拟播放）\n        if (!isPlaying) {\n            setTimeout(()=>{\n                setIsPlaying(false);\n            }, 3000);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all border border-gray-100 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-4xl\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(type)}`,\n                        children: t(`typeLabels.${type}`)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"text-xl font-medium text-gray-800 mb-2\",\n                children: name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: description\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handlePlay,\n                className: `w-full py-3 rounded-full transition-all ${isPlaying ? \"bg-indigo-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-indigo-100\"}`,\n                children: isPlaying ? t(\"buttons.pause\") : t(\"buttons.play\")\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CategoryCard/CategoryCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/HeroSection/HeroSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/HeroSection/HeroSection.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction HeroSection({ className = \"\" }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"landing\");\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useLocale)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `py-20 px-4 sm:px-6 lg:px-8 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-5xl md:text-6xl font-light text-gray-800 mb-6 leading-tight\",\n                    children: locale === \"zh\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            \"让\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-indigo-600\",\n                                children: t(\"hero.titleHighlight\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 15\n                            }, this),\n                            \"陪伴你的美梦\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            \"Let\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-indigo-600\",\n                                children: t(\"hero.titleHighlight\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this),\n                            \"Accompany Your Sweet Dreams\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl text-gray-600 mb-12 max-w-2xl mx-auto leading-relaxed\",\n                    children: t(\"hero.description\")\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: `/${locale}/sounds`,\n                            className: \"inline-block bg-indigo-600 text-white px-8 py-4 rounded-full text-lg hover:bg-indigo-700 transition-all transform hover:scale-105\",\n                            children: t(\"buttons.experience\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"border-2 border-indigo-600 text-indigo-600 px-8 py-4 rounded-full text-lg hover:bg-indigo-50 transition-colors\",\n                            children: t(\"buttons.learnMore\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HeroSection/HeroSection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageSelector/LanguageSelector.tsx":
/*!**************************************************************!*\
  !*** ./src/components/LanguageSelector/LanguageSelector.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst languages = [\n    {\n        code: \"zh\",\n        name: \"中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\"\n    },\n    {\n        code: \"en\",\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n    }\n];\nfunction LanguageSelector({ variant = \"header\", className = \"\" }) {\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const [isPending, startTransition] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useTransition)();\n    const handleLanguageChange = (newLocale)=>{\n        if (newLocale === locale) return;\n        startTransition(()=>{\n            // 替换当前路径中的语言代码\n            const newPathname = pathname.replace(`/${locale}`, `/${newLocale}`);\n            router.push(newPathname);\n        });\n    };\n    if (variant === \"footer\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex items-center ${className}`,\n            children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleLanguageChange(lang.code),\n                    disabled: isPending,\n                    className: `text-sm mx-1 px-2 py-1 rounded transition-colors ${locale === lang.code ? \"bg-indigo-600 text-white\" : \"hover:text-white\"} ${isPending ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                    children: [\n                        lang.flag,\n                        \" \",\n                        lang.name\n                    ]\n                }, lang.code, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/LanguageSelector/LanguageSelector.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/LanguageSelector/LanguageSelector.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n        value: locale,\n        onChange: (e)=>handleLanguageChange(e.target.value),\n        disabled: isPending,\n        className: `bg-white border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 ${isPending ? \"opacity-50 cursor-not-allowed\" : \"\"} ${className}`,\n        children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                value: lang.code,\n                children: [\n                    lang.flag,\n                    \" \",\n                    lang.name\n                ]\n            }, lang.code, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/LanguageSelector/LanguageSelector.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/LanguageSelector/LanguageSelector.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageSelector/LanguageSelector.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageSelector/index.ts":
/*!**************************************************!*\
  !*** ./src/components/LanguageSelector/index.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _LanguageSelector__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _LanguageSelector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./LanguageSelector */ \"(ssr)/./src/components/LanguageSelector/LanguageSelector.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9MYW5ndWFnZVNlbGVjdG9yL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTZDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm9pc2VzbGVlcC13ZWIvLi9zcmMvY29tcG9uZW50cy9MYW5ndWFnZVNlbGVjdG9yL2luZGV4LnRzPzc4ODQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gJy4vTGFuZ3VhZ2VTZWxlY3Rvcic7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageSelector/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Footer() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"landing\");\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useLocale)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"\\uD83C\\uDFB5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 18,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 17,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: t(\"siteName\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 max-w-md\",\n                                    children: t(\"footer.description\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                                    lineNumber: 28,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 27,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                                    lineNumber: 33,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t(\"footer.product\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: `/${locale}/sounds`,\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: t(\"footer.sounds\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: `/${locale}/mix`,\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: t(\"footer.mixing\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: t(\"footer.premium\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: t(\"footer.support\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: t(\"footer.help\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: t(\"footer.contact\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: t(\"footer.privacy\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: t(\"footer.copyright\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_LanguageSelector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LanguageSelector */ \"(ssr)/./src/components/LanguageSelector/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header({ variant = \"default\", showGetStarted = true }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)(\"landing\");\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white/80 backdrop-blur-sm border-b border-gray-100 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: `/${locale}`,\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-sm\",\n                                    children: \"\\uD83C\\uDFB5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-gray-800\",\n                                children: t(\"siteName\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex space-x-8\",\n                        children: variant === \"landing\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#sounds\",\n                                    className: \"text-gray-600 hover:text-indigo-600 font-medium transition-colors\",\n                                    children: t(\"nav.sounds\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#features\",\n                                    className: \"text-gray-600 hover:text-indigo-600 font-medium transition-colors\",\n                                    children: t(\"nav.features\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#blog\",\n                                    className: \"text-gray-600 hover:text-indigo-600 font-medium transition-colors\",\n                                    children: t(\"nav.blog\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: `/${locale}/sounds`,\n                                    className: \"text-gray-600 hover:text-indigo-600 font-medium transition-colors\",\n                                    children: t(\"nav.sounds\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: `/${locale}/mix`,\n                                    className: \"text-gray-600 hover:text-indigo-600 font-medium transition-colors\",\n                                    children: locale === \"zh\" ? \"混音器\" : \"Mixer\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: `/${locale}/favorites`,\n                                    className: \"text-gray-600 hover:text-indigo-600 font-medium transition-colors\",\n                                    children: locale === \"zh\" ? \"收藏\" : \"Favorites\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageSelector__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            showGetStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: `/${locale}/sounds`,\n                                className: \"bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors\",\n                                children: t(\"nav.getStarted\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/PageLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/PageLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/Layout/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Footer */ \"(ssr)/./src/components/Layout/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PageLayout({ children, headerVariant = \"default\", showGetStarted = true, showFooter = true, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                variant: headerVariant,\n                showGetStarted: showGetStarted\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/PageLayout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/PageLayout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            showFooter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/PageLayout.tsx\",\n                lineNumber: 28,\n                columnNumber: 22\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/Layout/PageLayout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvUGFnZUxheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRzhCO0FBQ0E7QUFVZixTQUFTRSxXQUFXLEVBQ2pDQyxRQUFRLEVBQ1JDLGdCQUFnQixTQUFTLEVBQ3pCQyxpQkFBaUIsSUFBSSxFQUNyQkMsYUFBYSxJQUFJLEVBQ2pCQyxZQUFZLEVBQUUsRUFDRTtJQUNoQixxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVyxDQUFDLHFFQUFxRSxFQUFFQSxVQUFVLENBQUM7OzBCQUNqRyw4REFBQ1AsK0NBQU1BO2dCQUFDUyxTQUFTTDtnQkFBZUMsZ0JBQWdCQTs7Ozs7OzBCQUNoRCw4REFBQ0s7Z0JBQUtILFdBQVU7MEJBQ2JKOzs7Ozs7WUFFRkcsNEJBQWMsOERBQUNMLCtDQUFNQTs7Ozs7Ozs7Ozs7QUFHNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub2lzZXNsZWVwLXdlYi8uL3NyYy9jb21wb25lbnRzL0xheW91dC9QYWdlTGF5b3V0LnRzeD9mMTEzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IEhlYWRlciBmcm9tICcuL0hlYWRlcic7XG5pbXBvcnQgRm9vdGVyIGZyb20gJy4vRm9vdGVyJztcblxuaW50ZXJmYWNlIFBhZ2VMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGU7XG4gIGhlYWRlclZhcmlhbnQ/OiAnZGVmYXVsdCcgfCAnbGFuZGluZyc7XG4gIHNob3dHZXRTdGFydGVkPzogYm9vbGVhbjtcbiAgc2hvd0Zvb3Rlcj86IGJvb2xlYW47XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZUxheW91dCh7IFxuICBjaGlsZHJlbiwgXG4gIGhlYWRlclZhcmlhbnQgPSAnZGVmYXVsdCcsXG4gIHNob3dHZXRTdGFydGVkID0gdHJ1ZSxcbiAgc2hvd0Zvb3RlciA9IHRydWUsXG4gIGNsYXNzTmFtZSA9ICcnXG59OiBQYWdlTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YG1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWluZGlnby01MCB2aWEtd2hpdGUgdG8tcHVycGxlLTUwICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgPEhlYWRlciB2YXJpYW50PXtoZWFkZXJWYXJpYW50fSBzaG93R2V0U3RhcnRlZD17c2hvd0dldFN0YXJ0ZWR9IC8+XG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9tYWluPlxuICAgICAge3Nob3dGb290ZXIgJiYgPEZvb3RlciAvPn1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJIZWFkZXIiLCJGb290ZXIiLCJQYWdlTGF5b3V0IiwiY2hpbGRyZW4iLCJoZWFkZXJWYXJpYW50Iiwic2hvd0dldFN0YXJ0ZWQiLCJzaG93Rm9vdGVyIiwiY2xhc3NOYW1lIiwiZGl2IiwidmFyaWFudCIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/PageLayout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"95df7b7bb871\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm9pc2VzbGVlcC13ZWIvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2JlNTUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5NWRmN2I3YmI4NzFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/[locale]/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/[locale]/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/[locale]/layout.tsx\",\"import\":\"Noto_Sans_SC\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-noto-sans-sc\",\"display\":\"swap\"}],\"variableName\":\"notoSansSC\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/[locale]/layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_SC\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-noto-sans-sc\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansSC\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n/routing */ \"(rsc)/./src/i18n/routing.ts\");\n\n\n\n\n\n\n\nfunction generateStaticParams() {\n    return _i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.map((locale)=>({\n            locale\n        }));\n}\nasync function generateMetadata({ params: { locale } }) {\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const meta = messages.meta;\n    return {\n        title: meta.title,\n        description: meta.description,\n        keywords: meta.keywords,\n        openGraph: {\n            title: meta.title,\n            description: meta.description,\n            url: `https://noisesleep.com${locale === \"en\" ? \"\" : \"/zh\"}`,\n            siteName: \"NoiseSleep\",\n            locale: locale === \"zh\" ? \"zh_CN\" : \"en_US\",\n            type: \"website\"\n        },\n        twitter: {\n            card: \"summary_large_image\",\n            title: meta.title,\n            description: meta.description\n        },\n        alternates: {\n            canonical: `https://noisesleep.com${locale === \"en\" ? \"\" : \"/zh\"}`,\n            languages: {\n                \"en\": \"https://noisesleep.com\",\n                \"zh\": \"https://noisesleep.com/zh\"\n            }\n        }\n    };\n}\nasync function LocaleLayout({ children, params: { locale } }) {\n    // 验证locale是否有效\n    if (!_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    // 获取翻译消息\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        dir: \"ltr\",\n        className: `${(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_5___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"en\",\n                        href: \"https://noisesleep.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"zh\",\n                        href: \"https://noisesleep.com/zh\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"x-default\",\n                        href: \"https://noisesleep.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#f59e0b\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://cdn.noisesleep.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"Content-Security-Policy\",\n                        content: \" default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; media-src 'self' blob: https://cdn.noisesleep.com; connect-src 'self' https://www.google-analytics.com; \"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `\n          ${locale === \"zh\" ? \"font-noto-sans-sc\" : \"font-inter\"}\n          antialiased\n          bg-white dark:bg-gray-900\n          text-gray-900 dark:text-gray-100\n          transition-colors duration-300\n        `,\n                \"data-locale\": locale,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    messages: messages,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/page.tsx":
/*!***********************************!*\
  !*** ./src/app/[locale]/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useLocale.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"(rsc)/./src/components/Layout/index.ts\");\n/* harmony import */ var _components_HeroSection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/HeroSection */ \"(rsc)/./src/components/HeroSection/index.ts\");\n/* harmony import */ var _components_CategoryCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CategoryCard */ \"(rsc)/./src/components/CategoryCard/index.ts\");\n/* harmony import */ var _components_FeatureCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/FeatureCard */ \"(rsc)/./src/components/FeatureCard/index.ts\");\n/* harmony import */ var _components_BlogCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/BlogCard */ \"(rsc)/./src/components/BlogCard/index.ts\");\n/* harmony import */ var _components_CTASection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/CTASection */ \"(rsc)/./src/components/CTASection/index.ts\");\n\n\n\n// 组件导入\n\n\n\n\n\n\nfunction HomePage({ params: { locale } }) {\n    // 启用静态渲染优化\n    (0,next_intl_server__WEBPACK_IMPORTED_MODULE_7__.setCachedRequestLocale)(locale);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(\"landing\");\n    const currentLocale = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n    // 白噪音分类数据\n    const whiteNoiseCategories = [\n        {\n            icon: \"\\uD83C\\uDF27️\",\n            type: \"rain\"\n        },\n        {\n            icon: \"\\uD83C\\uDF0A\",\n            type: \"nature\"\n        },\n        {\n            icon: \"\\uD83C\\uDF32\",\n            type: \"nature\"\n        },\n        {\n            icon: \"☕\",\n            type: \"places\"\n        },\n        {\n            icon: \"\\uD83D\\uDD25\",\n            type: \"nature\"\n        },\n        {\n            icon: \"\\uD83D\\uDCA8\",\n            type: \"nature\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__.PageLayout, {\n        headerVariant: \"landing\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeroSection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"sounds\",\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: t(\"categories.title\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: t(\"categories.subtitle\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\",\n                            children: whiteNoiseCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CategoryCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    icon: category.icon,\n                                    type: category.type,\n                                    locale: currentLocale\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-20 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: t(\"features.title\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: t(\"features.subtitle\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FeatureCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    icon: \"\\uD83E\\uDDE0\",\n                                    title: t(\"features.scientific.title\"),\n                                    description: t(\"features.scientific.description\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FeatureCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    icon: \"\\uD83C\\uDF9A️\",\n                                    title: t(\"features.customizable.title\"),\n                                    description: t(\"features.customizable.description\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FeatureCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCF1\",\n                                    title: t(\"features.responsive.title\"),\n                                    description: t(\"features.responsive.description\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FeatureCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    icon: \"\\uD83C\\uDF19\",\n                                    title: t(\"features.sleepMode.title\"),\n                                    description: t(\"features.sleepMode.description\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FeatureCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    icon: \"\\uD83C\\uDFAF\",\n                                    title: t(\"features.focus.title\"),\n                                    description: t(\"features.focus.description\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FeatureCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    icon: \"\\uD83C\\uDF0D\",\n                                    title: t(\"features.multilingual.title\"),\n                                    description: t(\"features.multilingual.description\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"blog\",\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: t(\"blog.title\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: t(\"blog.subtitle\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BlogCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    title: t(\"blog.posts.sleepScience.title\"),\n                                    excerpt: t(\"blog.posts.sleepScience.excerpt\"),\n                                    date: t(\"blog.posts.sleepScience.date\"),\n                                    readTime: t(\"blog.posts.sleepScience.readTime\"),\n                                    image: \"/images/blog/sleep-science.jpg\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BlogCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    title: t(\"blog.posts.whiteNoise.title\"),\n                                    excerpt: t(\"blog.posts.whiteNoise.excerpt\"),\n                                    date: t(\"blog.posts.whiteNoise.date\"),\n                                    readTime: t(\"blog.posts.whiteNoise.readTime\"),\n                                    image: \"/images/blog/white-noise.jpg\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BlogCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    title: t(\"blog.posts.productivity.title\"),\n                                    excerpt: t(\"blog.posts.productivity.excerpt\"),\n                                    date: t(\"blog.posts.productivity.date\"),\n                                    readTime: t(\"blog.posts.productivity.readTime\"),\n                                    image: \"/images/blog/productivity.jpg\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CTASection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/page.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBdUQ7QUFDSDtBQUVwRCxPQUFPO0FBQzBDO0FBQ0U7QUFDRTtBQUNGO0FBQ047QUFDSTtBQU1sQyxTQUFTUyxTQUFTLEVBQUVDLFFBQVEsRUFBRUMsTUFBTSxFQUFFLEVBQVM7SUFDNUQsV0FBVztJQUNYVCx3RUFBZ0JBLENBQUNTO0lBRWpCLE1BQU1DLElBQUlaLHFEQUFlQSxDQUFDO0lBQzFCLE1BQU1hLGdCQUFnQloscURBQVNBO0lBRS9CLFVBQVU7SUFDVixNQUFNYSx1QkFBdUI7UUFDM0I7WUFBRUMsTUFBTTtZQUFPQyxNQUFNO1FBQU87UUFDNUI7WUFBRUQsTUFBTTtZQUFNQyxNQUFNO1FBQVM7UUFDN0I7WUFBRUQsTUFBTTtZQUFNQyxNQUFNO1FBQVM7UUFDN0I7WUFBRUQsTUFBTTtZQUFLQyxNQUFNO1FBQVM7UUFDNUI7WUFBRUQsTUFBTTtZQUFNQyxNQUFNO1FBQVM7UUFDN0I7WUFBRUQsTUFBTTtZQUFNQyxNQUFNO1FBQVM7S0FDOUI7SUFFRCxxQkFDRSw4REFBQ2IsMERBQVVBO1FBQUNjLGVBQWM7OzBCQUd4Qiw4REFBQ2IsK0RBQVdBOzs7OzswQkFHWiw4REFBQ2M7Z0JBQVFDLElBQUc7Z0JBQVNDLFdBQVU7MEJBQzdCLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0U7b0NBQUdGLFdBQVU7OENBQ1hSLEVBQUU7Ozs7Ozs4Q0FFTCw4REFBQ1c7b0NBQUVILFdBQVU7OENBQ1ZSLEVBQUU7Ozs7Ozs7Ozs7OztzQ0FJUCw4REFBQ1M7NEJBQUlELFdBQVU7c0NBQ1pOLHFCQUFxQlUsR0FBRyxDQUFDLENBQUNDLFVBQVVDLHNCQUNuQyw4REFBQ3JCLGdFQUFZQTtvQ0FFWFUsTUFBTVUsU0FBU1YsSUFBSTtvQ0FDbkJDLE1BQU1TLFNBQVNULElBQUk7b0NBQ25CTCxRQUFRRTttQ0FISGE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFXZiw4REFBQ1I7Z0JBQVFDLElBQUc7Z0JBQVdDLFdBQVU7MEJBQy9CLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0U7b0NBQUdGLFdBQVU7OENBQ1hSLEVBQUU7Ozs7Ozs4Q0FFTCw4REFBQ1c7b0NBQUVILFdBQVU7OENBQ1ZSLEVBQUU7Ozs7Ozs7Ozs7OztzQ0FJUCw4REFBQ1M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDZCwrREFBV0E7b0NBQ1ZTLE1BQUs7b0NBQ0xZLE9BQU9mLEVBQUU7b0NBQ1RnQixhQUFhaEIsRUFBRTs7Ozs7OzhDQUVqQiw4REFBQ04sK0RBQVdBO29DQUNWUyxNQUFLO29DQUNMWSxPQUFPZixFQUFFO29DQUNUZ0IsYUFBYWhCLEVBQUU7Ozs7Ozs4Q0FFakIsOERBQUNOLCtEQUFXQTtvQ0FDVlMsTUFBSztvQ0FDTFksT0FBT2YsRUFBRTtvQ0FDVGdCLGFBQWFoQixFQUFFOzs7Ozs7OENBRWpCLDhEQUFDTiwrREFBV0E7b0NBQ1ZTLE1BQUs7b0NBQ0xZLE9BQU9mLEVBQUU7b0NBQ1RnQixhQUFhaEIsRUFBRTs7Ozs7OzhDQUVqQiw4REFBQ04sK0RBQVdBO29DQUNWUyxNQUFLO29DQUNMWSxPQUFPZixFQUFFO29DQUNUZ0IsYUFBYWhCLEVBQUU7Ozs7Ozs4Q0FFakIsOERBQUNOLCtEQUFXQTtvQ0FDVlMsTUFBSztvQ0FDTFksT0FBT2YsRUFBRTtvQ0FDVGdCLGFBQWFoQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPdkIsOERBQUNNO2dCQUFRQyxJQUFHO2dCQUFPQyxXQUFVOzBCQUMzQiw0RUFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNFO29DQUFHRixXQUFVOzhDQUNYUixFQUFFOzs7Ozs7OENBRUwsOERBQUNXO29DQUFFSCxXQUFVOzhDQUNWUixFQUFFOzs7Ozs7Ozs7Ozs7c0NBSVAsOERBQUNTOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ2IsNERBQVFBO29DQUNQb0IsT0FBT2YsRUFBRTtvQ0FDVGlCLFNBQVNqQixFQUFFO29DQUNYa0IsTUFBTWxCLEVBQUU7b0NBQ1JtQixVQUFVbkIsRUFBRTtvQ0FDWm9CLE9BQU07Ozs7Ozs4Q0FFUiw4REFBQ3pCLDREQUFRQTtvQ0FDUG9CLE9BQU9mLEVBQUU7b0NBQ1RpQixTQUFTakIsRUFBRTtvQ0FDWGtCLE1BQU1sQixFQUFFO29DQUNSbUIsVUFBVW5CLEVBQUU7b0NBQ1pvQixPQUFNOzs7Ozs7OENBRVIsOERBQUN6Qiw0REFBUUE7b0NBQ1BvQixPQUFPZixFQUFFO29DQUNUaUIsU0FBU2pCLEVBQUU7b0NBQ1hrQixNQUFNbEIsRUFBRTtvQ0FDUm1CLFVBQVVuQixFQUFFO29DQUNab0IsT0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT2QsOERBQUN4Qiw4REFBVUE7Ozs7Ozs7Ozs7O0FBR2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm9pc2VzbGVlcC13ZWIvLi9zcmMvYXBwL1tsb2NhbGVdL3BhZ2UudHN4P2E0N2IiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zLCB1c2VMb2NhbGUgfSBmcm9tICduZXh0LWludGwnO1xuaW1wb3J0IHsgc2V0UmVxdWVzdExvY2FsZSB9IGZyb20gJ25leHQtaW50bC9zZXJ2ZXInO1xuXG4vLyDnu4Tku7blr7zlhaVcbmltcG9ydCB7IFBhZ2VMYXlvdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvTGF5b3V0JztcbmltcG9ydCBIZXJvU2VjdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvSGVyb1NlY3Rpb24nO1xuaW1wb3J0IENhdGVnb3J5Q2FyZCBmcm9tICdAL2NvbXBvbmVudHMvQ2F0ZWdvcnlDYXJkJztcbmltcG9ydCBGZWF0dXJlQ2FyZCBmcm9tICdAL2NvbXBvbmVudHMvRmVhdHVyZUNhcmQnO1xuaW1wb3J0IEJsb2dDYXJkIGZyb20gJ0AvY29tcG9uZW50cy9CbG9nQ2FyZCc7XG5pbXBvcnQgQ1RBU2VjdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvQ1RBU2VjdGlvbic7XG5cbnR5cGUgUHJvcHMgPSB7XG4gIHBhcmFtczogeyBsb2NhbGU6IHN0cmluZyB9O1xufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZVBhZ2UoeyBwYXJhbXM6IHsgbG9jYWxlIH0gfTogUHJvcHMpIHtcbiAgLy8g5ZCv55So6Z2Z5oCB5riy5p+T5LyY5YyWXG4gIHNldFJlcXVlc3RMb2NhbGUobG9jYWxlKTtcblxuICBjb25zdCB0ID0gdXNlVHJhbnNsYXRpb25zKCdsYW5kaW5nJyk7XG4gIGNvbnN0IGN1cnJlbnRMb2NhbGUgPSB1c2VMb2NhbGUoKTtcblxuICAvLyDnmb3lmarpn7PliIbnsbvmlbDmja5cbiAgY29uc3Qgd2hpdGVOb2lzZUNhdGVnb3JpZXMgPSBbXG4gICAgeyBpY29uOiAn8J+Mp++4jycsIHR5cGU6ICdyYWluJyB9LFxuICAgIHsgaWNvbjogJ/CfjIonLCB0eXBlOiAnbmF0dXJlJyB9LFxuICAgIHsgaWNvbjogJ/CfjLInLCB0eXBlOiAnbmF0dXJlJyB9LFxuICAgIHsgaWNvbjogJ+KYlScsIHR5cGU6ICdwbGFjZXMnIH0sXG4gICAgeyBpY29uOiAn8J+UpScsIHR5cGU6ICduYXR1cmUnIH0sXG4gICAgeyBpY29uOiAn8J+SqCcsIHR5cGU6ICduYXR1cmUnIH0sXG4gIF07XG5cbiAgcmV0dXJuIChcbiAgICA8UGFnZUxheW91dCBoZWFkZXJWYXJpYW50PVwibGFuZGluZ1wiPnsvKiDkvb/nlKhMYW5kaW5n6aG16Z2i55qESGVhZGVy5qC35byPICovfVxuXG4gICAgICB7LyogSGVybyBTZWN0aW9uICovfVxuICAgICAgPEhlcm9TZWN0aW9uIC8+XG5cbiAgICAgIHsvKiDnmb3lmarpn7PliIbnsbvlsZXnpLogKi99XG4gICAgICA8c2VjdGlvbiBpZD1cInNvdW5kc1wiIGNsYXNzTmFtZT1cInB5LTIwIGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTE2XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWQ6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICB7dCgnY2F0ZWdvcmllcy50aXRsZScpfVxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTYwMCBtYXgtdy0zeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICB7dCgnY2F0ZWdvcmllcy5zdWJ0aXRsZScpfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy0zIGxnOmdyaWQtY29scy02IGdhcC02XCI+XG4gICAgICAgICAgICB7d2hpdGVOb2lzZUNhdGVnb3JpZXMubWFwKChjYXRlZ29yeSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPENhdGVnb3J5Q2FyZFxuICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgaWNvbj17Y2F0ZWdvcnkuaWNvbn1cbiAgICAgICAgICAgICAgICB0eXBlPXtjYXRlZ29yeS50eXBlfVxuICAgICAgICAgICAgICAgIGxvY2FsZT17Y3VycmVudExvY2FsZX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIOeJueiJsuWKn+iDvSAqL31cbiAgICAgIDxzZWN0aW9uIGlkPVwiZmVhdHVyZXNcIiBjbGFzc05hbWU9XCJweS0yMCBiZy1ncmF5LTUwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTE2XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWQ6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICB7dCgnZmVhdHVyZXMudGl0bGUnKX1cbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS02MDAgbWF4LXctM3hsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAge3QoJ2ZlYXR1cmVzLnN1YnRpdGxlJyl9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICAgIDxGZWF0dXJlQ2FyZFxuICAgICAgICAgICAgICBpY29uPVwi8J+noFwiXG4gICAgICAgICAgICAgIHRpdGxlPXt0KCdmZWF0dXJlcy5zY2llbnRpZmljLnRpdGxlJyl9XG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uPXt0KCdmZWF0dXJlcy5zY2llbnRpZmljLmRlc2NyaXB0aW9uJyl9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPEZlYXR1cmVDYXJkXG4gICAgICAgICAgICAgIGljb249XCLwn46a77iPXCJcbiAgICAgICAgICAgICAgdGl0bGU9e3QoJ2ZlYXR1cmVzLmN1c3RvbWl6YWJsZS50aXRsZScpfVxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbj17dCgnZmVhdHVyZXMuY3VzdG9taXphYmxlLmRlc2NyaXB0aW9uJyl9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPEZlYXR1cmVDYXJkXG4gICAgICAgICAgICAgIGljb249XCLwn5OxXCJcbiAgICAgICAgICAgICAgdGl0bGU9e3QoJ2ZlYXR1cmVzLnJlc3BvbnNpdmUudGl0bGUnKX1cbiAgICAgICAgICAgICAgZGVzY3JpcHRpb249e3QoJ2ZlYXR1cmVzLnJlc3BvbnNpdmUuZGVzY3JpcHRpb24nKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8RmVhdHVyZUNhcmRcbiAgICAgICAgICAgICAgaWNvbj1cIvCfjJlcIlxuICAgICAgICAgICAgICB0aXRsZT17dCgnZmVhdHVyZXMuc2xlZXBNb2RlLnRpdGxlJyl9XG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uPXt0KCdmZWF0dXJlcy5zbGVlcE1vZGUuZGVzY3JpcHRpb24nKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8RmVhdHVyZUNhcmRcbiAgICAgICAgICAgICAgaWNvbj1cIvCfjq9cIlxuICAgICAgICAgICAgICB0aXRsZT17dCgnZmVhdHVyZXMuZm9jdXMudGl0bGUnKX1cbiAgICAgICAgICAgICAgZGVzY3JpcHRpb249e3QoJ2ZlYXR1cmVzLmZvY3VzLmRlc2NyaXB0aW9uJyl9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPEZlYXR1cmVDYXJkXG4gICAgICAgICAgICAgIGljb249XCLwn4yNXCJcbiAgICAgICAgICAgICAgdGl0bGU9e3QoJ2ZlYXR1cmVzLm11bHRpbGluZ3VhbC50aXRsZScpfVxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbj17dCgnZmVhdHVyZXMubXVsdGlsaW5ndWFsLmRlc2NyaXB0aW9uJyl9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIOWNmuWuoumihOiniCAqL31cbiAgICAgIDxzZWN0aW9uIGlkPVwiYmxvZ1wiIGNsYXNzTmFtZT1cInB5LTIwIGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTE2XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWQ6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICB7dCgnYmxvZy50aXRsZScpfVxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTYwMCBtYXgtdy0zeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICB7dCgnYmxvZy5zdWJ0aXRsZScpfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC04XCI+XG4gICAgICAgICAgICA8QmxvZ0NhcmRcbiAgICAgICAgICAgICAgdGl0bGU9e3QoJ2Jsb2cucG9zdHMuc2xlZXBTY2llbmNlLnRpdGxlJyl9XG4gICAgICAgICAgICAgIGV4Y2VycHQ9e3QoJ2Jsb2cucG9zdHMuc2xlZXBTY2llbmNlLmV4Y2VycHQnKX1cbiAgICAgICAgICAgICAgZGF0ZT17dCgnYmxvZy5wb3N0cy5zbGVlcFNjaWVuY2UuZGF0ZScpfVxuICAgICAgICAgICAgICByZWFkVGltZT17dCgnYmxvZy5wb3N0cy5zbGVlcFNjaWVuY2UucmVhZFRpbWUnKX1cbiAgICAgICAgICAgICAgaW1hZ2U9XCIvaW1hZ2VzL2Jsb2cvc2xlZXAtc2NpZW5jZS5qcGdcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxCbG9nQ2FyZFxuICAgICAgICAgICAgICB0aXRsZT17dCgnYmxvZy5wb3N0cy53aGl0ZU5vaXNlLnRpdGxlJyl9XG4gICAgICAgICAgICAgIGV4Y2VycHQ9e3QoJ2Jsb2cucG9zdHMud2hpdGVOb2lzZS5leGNlcnB0Jyl9XG4gICAgICAgICAgICAgIGRhdGU9e3QoJ2Jsb2cucG9zdHMud2hpdGVOb2lzZS5kYXRlJyl9XG4gICAgICAgICAgICAgIHJlYWRUaW1lPXt0KCdibG9nLnBvc3RzLndoaXRlTm9pc2UucmVhZFRpbWUnKX1cbiAgICAgICAgICAgICAgaW1hZ2U9XCIvaW1hZ2VzL2Jsb2cvd2hpdGUtbm9pc2UuanBnXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8QmxvZ0NhcmRcbiAgICAgICAgICAgICAgdGl0bGU9e3QoJ2Jsb2cucG9zdHMucHJvZHVjdGl2aXR5LnRpdGxlJyl9XG4gICAgICAgICAgICAgIGV4Y2VycHQ9e3QoJ2Jsb2cucG9zdHMucHJvZHVjdGl2aXR5LmV4Y2VycHQnKX1cbiAgICAgICAgICAgICAgZGF0ZT17dCgnYmxvZy5wb3N0cy5wcm9kdWN0aXZpdHkuZGF0ZScpfVxuICAgICAgICAgICAgICByZWFkVGltZT17dCgnYmxvZy5wb3N0cy5wcm9kdWN0aXZpdHkucmVhZFRpbWUnKX1cbiAgICAgICAgICAgICAgaW1hZ2U9XCIvaW1hZ2VzL2Jsb2cvcHJvZHVjdGl2aXR5LmpwZ1wiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIENUQSBTZWN0aW9uICovfVxuICAgICAgPENUQVNlY3Rpb24gLz5cbiAgICA8L1BhZ2VMYXlvdXQ+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlVHJhbnNsYXRpb25zIiwidXNlTG9jYWxlIiwic2V0UmVxdWVzdExvY2FsZSIsIlBhZ2VMYXlvdXQiLCJIZXJvU2VjdGlvbiIsIkNhdGVnb3J5Q2FyZCIsIkZlYXR1cmVDYXJkIiwiQmxvZ0NhcmQiLCJDVEFTZWN0aW9uIiwiSG9tZVBhZ2UiLCJwYXJhbXMiLCJsb2NhbGUiLCJ0IiwiY3VycmVudExvY2FsZSIsIndoaXRlTm9pc2VDYXRlZ29yaWVzIiwiaWNvbiIsInR5cGUiLCJoZWFkZXJWYXJpYW50Iiwic2VjdGlvbiIsImlkIiwiY2xhc3NOYW1lIiwiZGl2IiwiaDIiLCJwIiwibWFwIiwiY2F0ZWdvcnkiLCJpbmRleCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJleGNlcnB0IiwiZGF0ZSIsInJlYWRUaW1lIiwiaW1hZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUdNQTtBQUZpQjtBQUlSLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztrQkFDQyw0RUFBQ0M7WUFBS0MsV0FBV0wsK0pBQWU7c0JBQzdCRTs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJztcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbD5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/BlogCard/BlogCard.tsx":
/*!**********************************************!*\
  !*** ./src/components/BlogCard/BlogCard.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction BlogCard({ post, className = \"\", onClick }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: `bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all cursor-pointer ${className}`,\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"text-xl font-medium text-gray-800 mb-3 hover:text-indigo-600 transition-colors\",\n                children: post.title\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/BlogCard/BlogCard.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 mb-4 leading-relaxed\",\n                children: post.excerpt\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/BlogCard/BlogCard.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center text-sm text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: post.date\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/BlogCard/BlogCard.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: post.readTime\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/BlogCard/BlogCard.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/BlogCard/BlogCard.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/BlogCard/BlogCard.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/BlogCard/BlogCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/BlogCard/index.ts":
/*!******************************************!*\
  !*** ./src/components/BlogCard/index.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _BlogCard__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _BlogCard__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BlogCard */ \"(rsc)/./src/components/BlogCard/BlogCard.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9CbG9nQ2FyZC9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQyIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2NvbXBvbmVudHMvQmxvZ0NhcmQvaW5kZXgudHM/YjM1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IH0gZnJvbSAnLi9CbG9nQ2FyZCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/BlogCard/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/CTASection/CTASection.tsx":
/*!**************************************************!*\
  !*** ./src/components/CTASection/CTASection.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/CTASection/CTASection.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/CTASection/index.ts":
/*!********************************************!*\
  !*** ./src/components/CTASection/index.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _CTASection__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _CTASection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CTASection */ \"(rsc)/./src/components/CTASection/CTASection.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9DVEFTZWN0aW9uL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm9pc2VzbGVlcC13ZWIvLi9zcmMvY29tcG9uZW50cy9DVEFTZWN0aW9uL2luZGV4LnRzP2M4ZmUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gJy4vQ1RBU2VjdGlvbic7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/CTASection/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/CategoryCard/CategoryCard.tsx":
/*!******************************************************!*\
  !*** ./src/components/CategoryCard/CategoryCard.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/CategoryCard/CategoryCard.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/CategoryCard/index.ts":
/*!**********************************************!*\
  !*** ./src/components/CategoryCard/index.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _CategoryCard__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _CategoryCard__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CategoryCard */ \"(rsc)/./src/components/CategoryCard/CategoryCard.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9DYXRlZ29yeUNhcmQvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub2lzZXNsZWVwLXdlYi8uL3NyYy9jb21wb25lbnRzL0NhdGVnb3J5Q2FyZC9pbmRleC50cz83NGFmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tICcuL0NhdGVnb3J5Q2FyZCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/CategoryCard/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/FeatureCard/FeatureCard.tsx":
/*!****************************************************!*\
  !*** ./src/components/FeatureCard/FeatureCard.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeatureCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction FeatureCard({ icon, title, description, bgColor = \"bg-indigo-100\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `text-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `w-16 h-16 ${bgColor} rounded-full flex items-center justify-center mx-auto mb-4`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-2xl\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/FeatureCard/FeatureCard.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/FeatureCard/FeatureCard.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"text-xl font-medium text-gray-800 mb-2\",\n                children: title\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/FeatureCard/FeatureCard.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600\",\n                children: description\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/FeatureCard/FeatureCard.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/FeatureCard/FeatureCard.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9GZWF0dXJlQ2FyZC9GZWF0dXJlQ2FyZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQVFlLFNBQVNBLFlBQVksRUFDbENDLElBQUksRUFDSkMsS0FBSyxFQUNMQyxXQUFXLEVBQ1hDLFVBQVUsZUFBZSxFQUN6QkMsWUFBWSxFQUFFLEVBQ0c7SUFDakIscUJBQ0UsOERBQUNDO1FBQUlELFdBQVcsQ0FBQyxZQUFZLEVBQUVBLFVBQVUsQ0FBQzs7MEJBQ3hDLDhEQUFDQztnQkFBSUQsV0FBVyxDQUFDLFVBQVUsRUFBRUQsUUFBUSwyREFBMkQsQ0FBQzswQkFDL0YsNEVBQUNHO29CQUFLRixXQUFVOzhCQUFZSjs7Ozs7Ozs7Ozs7MEJBRTlCLDhEQUFDTztnQkFBR0gsV0FBVTswQkFBMENIOzs7Ozs7MEJBQ3hELDhEQUFDTztnQkFBRUosV0FBVTswQkFBaUJGOzs7Ozs7Ozs7Ozs7QUFHcEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub2lzZXNsZWVwLXdlYi8uL3NyYy9jb21wb25lbnRzL0ZlYXR1cmVDYXJkL0ZlYXR1cmVDYXJkLnRzeD84YTNmIl0sInNvdXJjZXNDb250ZW50IjpbImludGVyZmFjZSBGZWF0dXJlQ2FyZFByb3BzIHtcbiAgaWNvbjogc3RyaW5nO1xuICB0aXRsZTogc3RyaW5nO1xuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICBiZ0NvbG9yPzogc3RyaW5nO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZlYXR1cmVDYXJkKHtcbiAgaWNvbixcbiAgdGl0bGUsXG4gIGRlc2NyaXB0aW9uLFxuICBiZ0NvbG9yID0gJ2JnLWluZGlnby0xMDAnLFxuICBjbGFzc05hbWUgPSAnJ1xufTogRmVhdHVyZUNhcmRQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC1jZW50ZXIgJHtjbGFzc05hbWV9YH0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMTYgaC0xNiAke2JnQ29sb3J9IHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTRgfT5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGxcIj57aWNvbn08L3NwYW4+XG4gICAgICA8L2Rpdj5cbiAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgbWItMlwiPnt0aXRsZX08L2g0PlxuICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPntkZXNjcmlwdGlvbn08L3A+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiRmVhdHVyZUNhcmQiLCJpY29uIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImJnQ29sb3IiLCJjbGFzc05hbWUiLCJkaXYiLCJzcGFuIiwiaDQiLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/FeatureCard/FeatureCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/FeatureCard/index.ts":
/*!*********************************************!*\
  !*** ./src/components/FeatureCard/index.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _FeatureCard__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _FeatureCard__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./FeatureCard */ \"(rsc)/./src/components/FeatureCard/FeatureCard.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9GZWF0dXJlQ2FyZC9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QyIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2NvbXBvbmVudHMvRmVhdHVyZUNhcmQvaW5kZXgudHM/ODg2NyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IH0gZnJvbSAnLi9GZWF0dXJlQ2FyZCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/FeatureCard/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/HeroSection/HeroSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/HeroSection/HeroSection.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/HeroSection/HeroSection.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/HeroSection/index.ts":
/*!*********************************************!*\
  !*** ./src/components/HeroSection/index.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _HeroSection__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _HeroSection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./HeroSection */ \"(rsc)/./src/components/HeroSection/HeroSection.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9IZXJvU2VjdGlvbi9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QyIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2NvbXBvbmVudHMvSGVyb1NlY3Rpb24vaW5kZXgudHM/NTIwMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IH0gZnJvbSAnLi9IZXJvU2VjdGlvbic7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/HeroSection/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/Layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Footer.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Layout/Header.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/Layout/PageLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/PageLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/Layout/PageLayout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/Layout/index.ts":
/*!****************************************!*\
  !*** ./src/components/Layout/index.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* reexport safe */ _Footer__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Header: () => (/* reexport safe */ _Header__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   PageLayout: () => (/* reexport safe */ _PageLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Header */ \"(rsc)/./src/components/Layout/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Footer */ \"(rsc)/./src/components/Layout/Footer.tsx\");\n/* harmony import */ var _PageLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PageLayout */ \"(rsc)/./src/components/Layout/PageLayout.tsx\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZDO0FBQ0E7QUFDUSIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2NvbXBvbmVudHMvTGF5b3V0L2luZGV4LnRzP2QzZWYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCBhcyBIZWFkZXIgfSBmcm9tICcuL0hlYWRlcic7XG5leHBvcnQgeyBkZWZhdWx0IGFzIEZvb3RlciB9IGZyb20gJy4vRm9vdGVyJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFnZUxheW91dCB9IGZyb20gJy4vUGFnZUxheW91dCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIkhlYWRlciIsIkZvb3RlciIsIlBhZ2VMYXlvdXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Layout/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/i18n/request.ts":
/*!*****************************!*\
  !*** ./src/i18n/request.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n/* harmony import */ var _routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./routing */ \"(rsc)/./src/i18n/routing.ts\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ locale })=>{\n    // 验证传入的locale是否有效\n    if (!_routing__WEBPACK_IMPORTED_MODULE_0__.routing.locales.includes(locale)) {\n        throw new Error(`Invalid locale: ${locale}`);\n    }\n    return {\n        messages: (await __webpack_require__(\"(rsc)/./src/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default,\n        timeZone: locale === \"zh\" ? \"Asia/Shanghai\" : \"America/New_York\",\n        now: new Date(),\n        formats: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                }\n            },\n            number: {\n                precise: {\n                    maximumFractionDigits: 2\n                }\n            }\n        }\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/request.ts\n");

/***/ }),

/***/ "(rsc)/./src/i18n/routing.ts":
/*!*****************************!*\
  !*** ./src/i18n/routing.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   routing: () => (/* binding */ routing)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/development/routing.js\");\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__.defineRouting)({\n    // 支持的语言列表\n    locales: [\n        \"en\",\n        \"zh\"\n    ],\n    // 默认语言\n    defaultLocale: \"en\",\n    // 语言前缀配置\n    localePrefix: {\n        mode: \"as-needed\",\n        prefixes: {\n            \"zh\": \"/zh\"\n        }\n    },\n    // 路径名配置\n    pathnames: {\n        \"/\": \"/\",\n        \"/about\": \"/about\",\n        \"/sounds\": \"/sounds\",\n        \"/sounds/[category]\": {\n            en: \"/sounds/[category]\",\n            zh: \"/sounds/[category]\"\n        },\n        \"/sounds/[category]/[sound]\": {\n            en: \"/sounds/[category]/[sound]\",\n            zh: \"/sounds/[category]/[sound]\"\n        },\n        \"/mix\": \"/mix\",\n        \"/favorites\": \"/favorites\",\n        \"/settings\": \"/settings\"\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/routing.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@formatjs","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/next-intl"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();